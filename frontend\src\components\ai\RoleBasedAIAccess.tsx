import React from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { 
  getUserAICapabilities, 
  getUserRoles, 
  getUserAIRateLimits,
  canAccessAICapability,
  AICapability 
} from '../../utils/roleBasedAI';
import { RTLText } from '../rtl';
import { Lock, Crown, Shield, TrendingUp, Users, MessageSquare, BarChart3, Settings } from 'lucide-react';

interface RoleBasedAIAccessProps {
  children: React.ReactNode;
  requiredCapability?: string;
  fallbackComponent?: React.ReactNode;
  showUpgradePrompt?: boolean;
}

/**
 * Component that controls access to AI features based on user roles
 */
const RoleBasedAIAccess: React.FC<RoleBasedAIAccessProps> = ({
  children,
  requiredCapability,
  fallbackComponent,
  showUpgradePrompt = true
}) => {
  const { user } = useAuth();
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // Check if user has access to the required capability
  const hasAccess = requiredCapability ? canAccessAICapability(user, requiredCapability) : true;

  if (hasAccess) {
    return <>{children}</>;
  }

  // Show fallback component if provided
  if (fallbackComponent) {
    return <>{fallbackComponent}</>;
  }

  // Show upgrade prompt if enabled
  if (showUpgradePrompt) {
    return <AIUpgradePrompt requiredCapability={requiredCapability} />;
  }

  // Default: show nothing
  return null;
};

/**
 * Component that shows AI capabilities for current user
 */
export const AICapabilitiesDisplay: React.FC = () => {
  const { user } = useAuth();
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const userRoles = getUserRoles(user);
  const capabilities = getUserAICapabilities(user);
  const rateLimits = getUserAIRateLimits(user);

  const getIconForCapability = (capability: AICapability) => {
    const iconMap: Record<string, React.ReactNode> = {
      basic_chat: <MessageSquare className="w-5 h-5" />,
      basic_analysis: <BarChart3 className="w-5 h-5" />,
      advanced_chat: <MessageSquare className="w-5 h-5" />,
      mentor_analysis: <TrendingUp className="w-5 h-5" />,
      mentee_support: <Users className="w-5 h-5" />,
      investment_analysis: <TrendingUp className="w-5 h-5" />,
      market_intelligence: <BarChart3 className="w-5 h-5" />,
      content_moderation: <Shield className="w-5 h-5" />,
      community_insights: <Users className="w-5 h-5" />,
      admin_analytics: <BarChart3 className="w-5 h-5" />,
      ai_management: <Settings className="w-5 h-5" />
    };
    return iconMap[capability.id] || <MessageSquare className="w-5 h-5" />;
  };

  const getRoleIcon = (role: string) => {
    const roleIcons: Record<string, React.ReactNode> = {
      admin: <Crown className="w-4 h-4 text-yellow-400" />,
      moderator: <Shield className="w-4 h-4 text-blue-400" />,
      mentor: <Users className="w-4 h-4 text-green-400" />,
      investor: <TrendingUp className="w-4 h-4 text-purple-400" />,
      user: <MessageSquare className="w-4 h-4 text-gray-400" />
    };
    return roleIcons[role] || <MessageSquare className="w-4 h-4 text-gray-400" />;
  };

  return (
    <div className="space-y-6">
      {/* User Roles */}
      <div className="glass-light rounded-xl p-6">
        <RTLText as="h3" className="text-lg font-semibold text-white mb-4">
          {t('ai.access.yourRoles', 'Your Roles')}
        </RTLText>
        <div className={`flex flex-wrap gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
          {userRoles.map(role => (
            <div
              key={role}
              className={`flex items-center px-3 py-2 rounded-lg glass-light border border-glass-border ${
                isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'
              }`}
            >
              {getRoleIcon(role)}
              <RTLText as="span" className="text-sm font-medium text-white capitalize">
                {t(`roles.${role}`, role)}
              </RTLText>
            </div>
          ))}
        </div>
      </div>

      {/* Rate Limits */}
      <div className="glass-light rounded-xl p-6">
        <RTLText as="h3" className="text-lg font-semibold text-white mb-4">
          {t('ai.access.rateLimits', 'Your AI Limits')}
        </RTLText>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="glass-light rounded-lg p-4 border border-glass-border">
            <RTLText as="div" className="text-sm text-glass-text-secondary">
              {t('ai.limits.chat', 'Chat Messages')}
            </RTLText>
            <RTLText as="div" className="text-2xl font-bold text-white">
              {rateLimits.chat}
            </RTLText>
            <RTLText as="div" className="text-xs text-glass-text-secondary">
              {t('ai.limits.perHour', 'per hour')}
            </RTLText>
          </div>
          <div className="glass-light rounded-lg p-4 border border-glass-border">
            <RTLText as="div" className="text-sm text-glass-text-secondary">
              {t('ai.limits.analysis', 'Analyses')}
            </RTLText>
            <RTLText as="div" className="text-2xl font-bold text-white">
              {rateLimits.analysis}
            </RTLText>
            <RTLText as="div" className="text-xs text-glass-text-secondary">
              {t('ai.limits.perDay', 'per day')}
            </RTLText>
          </div>
          <div className="glass-light rounded-lg p-4 border border-glass-border">
            <RTLText as="div" className="text-sm text-glass-text-secondary">
              {t('ai.limits.generation', 'Generations')}
            </RTLText>
            <RTLText as="div" className="text-2xl font-bold text-white">
              {rateLimits.generation}
            </RTLText>
            <RTLText as="div" className="text-xs text-glass-text-secondary">
              {t('ai.limits.perDay', 'per day')}
            </RTLText>
          </div>
        </div>
      </div>

      {/* Available Capabilities */}
      <div className="glass-light rounded-xl p-6">
        <RTLText as="h3" className="text-lg font-semibold text-white mb-4">
          {t('ai.access.availableFeatures', 'Available AI Features')}
        </RTLText>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {capabilities.map(capability => (
            <div
              key={capability.id}
              className="glass-light rounded-lg p-4 border border-glass-border"
            >
              <div className={`flex items-start ${isRTL ? 'space-x-reverse space-x-3' : 'space-x-3'}`}>
                <div className="flex-shrink-0 p-2 rounded-lg bg-purple-500/20">
                  {getIconForCapability(capability)}
                </div>
                <div className="flex-1 min-w-0">
                  <RTLText as="h4" className="font-medium text-white">
                    {capability.name}
                  </RTLText>
                  <RTLText as="p" className="text-sm text-glass-text-secondary mt-1">
                    {capability.description}
                  </RTLText>
                  {capability.rateLimit && (
                    <RTLText as="div" className="text-xs text-glass-text-secondary mt-2">
                      {t('ai.limits.limit', 'Limit')}: {capability.rateLimit.requests} {t(`ai.limits.${capability.rateLimit.period}`, capability.rateLimit.period)}
                    </RTLText>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

/**
 * Component that shows upgrade prompt for restricted AI features
 */
const AIUpgradePrompt: React.FC<{ requiredCapability?: string }> = ({ requiredCapability }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  return (
    <div className="glass-light rounded-xl p-8 text-center border border-glass-border">
      <div className="flex justify-center mb-4">
        <div className="p-3 rounded-full bg-purple-500/20">
          <Lock className="w-8 h-8 text-purple-400" />
        </div>
      </div>
      <RTLText as="h3" className="text-xl font-semibold text-white mb-2">
        {t('ai.access.restricted', 'AI Feature Restricted')}
      </RTLText>
      <RTLText as="p" className="text-glass-text-secondary mb-6">
        {t('ai.access.upgradeMessage', 'This AI feature requires a higher role or permission level. Contact an administrator to upgrade your access.')}
      </RTLText>
      <div className="space-y-2">
        <RTLText as="p" className="text-sm text-glass-text-secondary">
          {t('ai.access.availableRoles', 'Available to:')}
        </RTLText>
        <div className={`flex justify-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <span className="px-3 py-1 rounded-full bg-green-500/20 text-green-400 text-sm">
            {t('roles.mentor', 'Mentor')}
          </span>
          <span className="px-3 py-1 rounded-full bg-purple-500/20 text-purple-400 text-sm">
            {t('roles.investor', 'Investor')}
          </span>
          <span className="px-3 py-1 rounded-full bg-blue-500/20 text-blue-400 text-sm">
            {t('roles.moderator', 'Moderator')}
          </span>
          <span className="px-3 py-1 rounded-full bg-yellow-500/20 text-yellow-400 text-sm">
            {t('roles.admin', 'Admin')}
          </span>
        </div>
      </div>
    </div>
  );
};

export default RoleBasedAIAccess;
