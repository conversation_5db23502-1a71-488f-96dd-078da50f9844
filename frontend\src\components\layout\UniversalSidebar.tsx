/**
 * Universal Sidebar
 * Single sidebar component that adapts to ALL user types:
 * - Regular Users
 * - Admin Users  
 * - Super Admin Users
 * - Mentors, Investors, Moderators
 * 
 * Eliminates ALL sidebar duplications across the application
 */

import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '../../store/hooks';
import { logout } from '../../store/authSlice';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { createRoleManager, UserRole } from '../../utils/unifiedRoleManager';
import {
  Home, FileText, Lightbulb, Settings, User, X, LogOut, ChevronRight, ChevronLeft,
  Calendar, BookOpen, BarChart3, MessageSquare, Users, Shield, Briefcase,
  TrendingUp, Heart, Search, Bot, Zap, Globe, Award, Target, DollarSign,
  Database, Monitor, Lock, AlertTriangle, Activity, PieChart, UserCheck,
  Building, Rocket, Star, Gift, Mail, Phone, HelpCircle, Book, Video,
  Image, Music, Code, Palette, Camera, Headphones, Gamepad2, Coffee, Sparkles
} from 'lucide-react';

interface UniversalSidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
  onToggle?: () => void;
  variant?: 'desktop' | 'mobile';
  isCollapsed?: boolean;
}

interface NavItem {
  id: string;
  name: string;
  path: string;
  icon: React.ReactNode;
  userTypes: ('user' | 'admin' | 'super_admin' | 'mentor' | 'investor' | 'moderator')[];
  category: 'main' | 'content' | 'system' | 'security' | 'super_admin' | 'ai';
  riskLevel?: 'low' | 'medium' | 'high' | 'critical';
  children?: NavItem[];
}

// Use UserRole from unified role manager
type UserType = UserRole;

const UniversalSidebar: React.FC<UniversalSidebarProps> = ({
  isOpen = true,
  onClose,
  onToggle,
  variant = 'desktop',
  isCollapsed = false
}) => {
  const { user, isAuthenticated } = useAppSelector(state => state.auth);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // ✅ UNIFIED: Use the unified role manager for consistent role detection
  const getUserType = (): UserType => {
    const roleManager = createRoleManager(user);
    const primaryRole = roleManager.getPrimaryRole();

    // Debug logging in development
    const isDevelopment = import.meta.env?.DEV || window.location.hostname === 'localhost';
    if (isDevelopment) {
      console.log('🔍 UniversalSidebar getUserType debug:', roleManager.getDebugInfo());
    }

    return primaryRole;
  };

  const userType: UserType = getUserType();
  const roleManager = createRoleManager(user);

  // Type guard functions for better type safety
  const isSuperAdminType = (type: UserType): type is 'super_admin' => type === 'super_admin';
  const isAdminType = (type: UserType): type is 'admin' => type === 'admin';
  const isMentorType = (type: UserType): type is 'mentor' => type === 'mentor';
  const isInvestorType = (type: UserType): type is 'investor' => type === 'investor';

  // ROLE-SPECIFIC NAVIGATION - Each role gets their own dashboard and navigation
  const navItems: NavItem[] = [
    // Main Dashboard - Role-specific paths
    {
      id: 'dashboard',
      name: t('dashboard.title', 'Dashboard'),
      path: userType === 'super_admin' ? '/super_admin' :
            userType === 'admin' ? '/admin' :
            userType === 'moderator' ? '/dashboard/moderator' :
            userType === 'mentor' ? '/dashboard/mentor' :
            userType === 'investor' ? '/dashboard/investor' :
            '/dashboard',
      icon: <Home className="w-5 h-5" />,
      userTypes: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
      category: 'main'
    },

    // Business & Ideas - Only for users, mentors, and investors
    {
      id: 'business-ideas',
      name: t('businessIdeas.title', 'Business Ideas'),
      path: '/dashboard/business-ideas',
      icon: <Lightbulb className="w-5 h-5" />,
      userTypes: ['user', 'mentor', 'investor'], // Removed admin, super_admin, moderator
      category: 'main'
    },
    {
      id: 'business-plans',
      name: t('businessPlans.title', 'Business Plans'),
      path: '/dashboard/business-plans',
      icon: <FileText className="w-5 h-5" />,
      userTypes: ['user', 'mentor', 'investor'], // Removed admin, super_admin, moderator
      category: 'main'
    },
    {
      id: 'incubator',
      name: t('incubator.title', 'Incubator'),
      path: '/dashboard/incubator',
      icon: <Rocket className="w-5 h-5" />,
      userTypes: ['user', 'mentor', 'investor'], // Removed admin, super_admin, moderator
      category: 'main'
    },

    // Content & Resources - Different access levels
    {
      id: 'posts',
      name: t('posts.title', 'Posts'),
      path: '/dashboard/posts',
      icon: <MessageSquare className="w-5 h-5" />,
      userTypes: ['user', 'mentor', 'investor'], // Content creators and consumers
      category: 'content'
    },
    {
      id: 'events',
      name: t('events.title', 'Events'),
      path: '/dashboard/events',
      icon: <Calendar className="w-5 h-5" />,
      userTypes: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
      category: 'content'
    },
    {
      id: 'resources',
      name: t('resources.title', 'Resources'),
      path: '/dashboard/resources',
      icon: <BookOpen className="w-5 h-5" />,
      userTypes: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
      category: 'content'
    },
    {
      id: 'templates',
      name: t('templates.title', 'Templates'),
      path: '/dashboard/templates',
      icon: <FileText className="w-5 h-5" />,
      userTypes: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
      category: 'content'
    },

    // Analytics & Insights - Role-specific analytics
    {
      id: 'analytics',
      name: t('analytics.title', 'Analytics'),
      path: userType === 'mentor' ? '/dashboard/mentorship/analytics' :
            userType === 'investor' ? '/dashboard/investments/analytics' :
            userType === 'moderator' ? '/dashboard/moderation/analytics' :
            '/dashboard/analytics',
      icon: <BarChart3 className="w-5 h-5" />,
      userTypes: ['user', 'mentor', 'investor', 'moderator'], // Removed admin and super_admin
      category: 'main'
    },

    // Mentor-specific Features
    {
      id: 'mentorship',
      name: t('mentorship.title', 'Mentorship'),
      path: '/dashboard/mentorship',
      icon: <UserCheck className="w-5 h-5" />,
      userTypes: ['mentor'], // Only mentors
      category: 'main'
    },
    {
      id: 'mentees',
      name: t('mentees.title', 'My Mentees'),
      path: '/dashboard/mentor/mentees',
      icon: <Users className="w-5 h-5" />,
      userTypes: ['mentor'], // Only mentors
      category: 'main'
    },

    // Investor-specific Features
    {
      id: 'investments',
      name: t('investments.title', 'Investments'),
      path: '/dashboard/investments',
      icon: <TrendingUp className="w-5 h-5" />,
      userTypes: ['investor'], // Only investors
      category: 'main'
    },
    {
      id: 'portfolio',
      name: t('portfolio.title', 'Portfolio'),
      path: '/dashboard/investments/portfolio',
      icon: <PieChart className="w-5 h-5" />,
      userTypes: ['investor'], // Only investors
      category: 'main'
    },

    // Moderator-specific Features
    {
      id: 'moderation',
      name: t('moderation.title', 'Content Moderation'),
      path: '/dashboard/moderation/content',
      icon: <Shield className="w-5 h-5" />,
      userTypes: ['moderator'], // Only moderators
      category: 'main'
    },
    {
      id: 'reports',
      name: t('reports.title', 'Reports'),
      path: '/dashboard/moderation/reports',
      icon: <AlertTriangle className="w-5 h-5" />,
      userTypes: ['moderator'], // Only moderators
      category: 'main'
    },

    // AI Features - Available to content creators and users
    {
      id: 'ai-chat',
      name: t('ai.chat.title', 'AI Assistant'),
      path: '/chat/enhanced',
      icon: <Bot className="w-5 h-5" />,
      userTypes: ['user', 'mentor', 'investor'], // Only for business-focused users
      category: 'ai'
    },

    // Admin Features - Only for admins
    {
      id: 'user-management',
      name: t('admin.users.title', 'User Management'),
      path: '/admin/users',
      icon: <Users className="w-5 h-5" />,
      userTypes: ['admin'], // Only admins, not super_admin
      category: 'system'
    },
    {
      id: 'content-management',
      name: t('admin.content.title', 'Content Management'),
      path: '/admin/posts',
      icon: <FileText className="w-5 h-5" />,
      userTypes: ['admin'], // Only admins
      category: 'system'
    },

    // Super Admin Features - Only for super admins
    {
      id: 'system-management',
      name: t('superAdmin.system.title', 'System Management'),
      path: '/super_admin/system-management',
      icon: <Database className="w-5 h-5" />,
      userTypes: ['super_admin'], // Only super admins
      category: 'system',
      riskLevel: 'critical'
    },
    {
      id: 'user-impersonation',
      name: t('superAdmin.impersonation.title', 'User Impersonation'),
      path: '/super_admin/user-impersonation',
      icon: <Monitor className="w-5 h-5" />,
      userTypes: ['super_admin'], // Only super admins
      category: 'system',
      riskLevel: 'critical'
    },
    {
      id: 'ai-system-management',
      name: t('superAdmin.ai.title', 'AI System Management'),
      path: '/super_admin/ai-system-management',
      icon: <Bot className="w-5 h-5" />,
      userTypes: ['super_admin'], // Only super admins
      category: 'system',
      riskLevel: 'high'
    },

    // User Profile & Settings - Available to all roles
    {
      id: 'profile',
      name: t('profile.title', 'Profile'),
      path: userType === 'mentor' ? '/dashboard/mentorship/profile' :
            userType === 'investor' ? '/dashboard/investments/profile' :
            '/profile',
      icon: <User className="w-5 h-5" />,
      userTypes: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
      category: 'main'
    },
    {
      id: 'settings',
      name: t('settings.title', 'Settings'),
      path: '/settings',
      icon: <Settings className="w-5 h-5" />,
      userTypes: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
      category: 'main'
    }
  ];

  // ✅ UNIFIED: Use unified role manager for permission checking
  const hasPermissionForItem = (item: NavItem): boolean => {
    const roleManager = createRoleManager(user);

    // Must be authenticated
    if (!roleManager.isAuthenticated()) {
      return false;
    }

    // Check if user has any of the required roles for this item
    return roleManager.hasAnyRole(item.userTypes as UserRole[]);
  };

  // Filter items based on enhanced permission checking
  const filteredItems = navItems.filter(hasPermissionForItem);

  // Debug logging in development
  const isDevelopment = import.meta.env?.DEV || window.location.hostname === 'localhost';
  if (isDevelopment) {
    console.log('🔍 UniversalSidebar navigation filtering debug:', {
      userType,
      totalNavItems: navItems.length,
      filteredItemsCount: filteredItems.length,
      filteredItemNames: filteredItems.map(item => item.name),
      allNavItems: navItems.map(item => ({
        name: item.name,
        userTypes: item.userTypes,
        hasPermission: hasPermissionForItem(item)
      }))
    });
  }

  // Organize items by category for better navigation structure
  const organizeItemsByCategory = (items: NavItem[]) => {
    const categories = {
      main: items.filter(item => item.category === 'main'),
      content: items.filter(item => item.category === 'content'),
      ai: items.filter(item => item.category === 'ai'),
      system: items.filter(item => item.category === 'system'),
      security: items.filter(item => item.category === 'security'),
      super_admin: items.filter(item => item.category === 'super_admin')
    };

    return categories;
  };

  const categorizedItems = organizeItemsByCategory(filteredItems);

  // Permission filtering for troubleshooting in development
  React.useEffect(() => {
    // Debug info available in development mode only
  }, [userType, filteredItems.length, user, isAuthenticated]);



  const handleLogout = () => {
    dispatch(logout());
    navigate('/login');
    if (onClose) onClose();
  };

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };



  const getRiskBadge = (riskLevel?: string) => {
    if (!riskLevel) return null;

    const colors = {
      low: 'bg-green-500/20 text-green-300 border border-green-500/30',
      medium: 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30',
      high: 'bg-orange-500/20 text-orange-300 border border-orange-500/30',
      critical: 'bg-red-500/20 text-red-300 border border-red-500/30'
    };

    const icons = {
      low: '🟢',
      medium: '🟡',
      high: '🟠',
      critical: '🔴'
    };

    return (
      <span className={`px-2 py-1 text-xs rounded-lg font-medium ${colors[riskLevel as keyof typeof colors]} flex items-center gap-1`}>
        <span>{icons[riskLevel as keyof typeof icons]}</span>
        {riskLevel.toUpperCase()}
      </span>
    );
  };

  const getUserTypeDisplay = () => {
    if (isSuperAdminType(userType)) {
      return { name: 'Super Administrator', color: 'text-red-600', icon: <User className="w-5 h-5" /> };
    }
    if (isAdminType(userType)) {
      return { name: 'Administrator', color: 'text-blue-600', icon: <User className="w-5 h-5" /> };
    }
    if (isMentorType(userType)) {
      return { name: 'Mentor', color: 'text-green-600', icon: <User className="w-5 h-5" /> };
    }
    if (isInvestorType(userType)) {
      return { name: 'Investor', color: 'text-purple-600', icon: <User className="w-5 h-5" /> };
    }
    if (userType === 'moderator') {
      return { name: 'Moderator', color: 'text-orange-600', icon: <User className="w-5 h-5" /> };
    }
    return { name: 'User', color: 'text-gray-600', icon: <User className="w-5 h-5" /> };
  };

  const userDisplay = getUserTypeDisplay();

  const sidebarContent = (
    <div className={`flex flex-col h-full universal-sidebar ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className={`sidebar-header flex items-center justify-between p-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
          {/* App Logo - Match login page styling */}
          <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="app-logo w-12 h-12 rounded-full flex items-center justify-center shadow-lg">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <h1 className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400">
                {t('app.name', 'Yasmeen AI')}
              </h1>
              <p className="text-xs text-white/70">{userDisplay.name}</p>
            </div>
          </div>
        </div>

        {/* User Info */}
        <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`p-1.5 rounded-lg bg-gradient-to-br ${
            isSuperAdminType(userType) ? 'from-red-500 to-red-600' :
            isAdminType(userType) ? 'from-blue-500 to-blue-600' :
            isMentorType(userType) ? 'from-green-500 to-green-600' :
            isInvestorType(userType) ? 'from-purple-500 to-purple-600' :
            userType === 'moderator' ? 'from-orange-500 to-orange-600' :
            'from-gray-500 to-gray-600'
          }`}>
            {userDisplay.icon}
          </div>
          <div className={isRTL ? 'text-left' : 'text-right'}>
            <p className="text-xs text-white/90">{user?.username}</p>
            <p className="text-xs text-white/60">{userDisplay.name}</p>
          </div>
        </div>
        {variant === 'mobile' && onClose && (
          <button onClick={onClose} className="p-2 hover:bg-white/10 rounded-lg transition-colors glass-light border border-white/20">
            <X className="w-5 h-5 text-white/70" />
          </button>
        )}
      </div>

      {/* Navigation - Organized by Category */}
      <div className="flex-1 overflow-y-auto p-4 custom-scrollbar">
        <div className="space-y-6">
          {/* Main Navigation */}
          {categorizedItems.main.length > 0 && (
            <div className="space-y-2">
              {categorizedItems.main.map(item => (
                <a
                  key={item.id}
                  href={item.path}
                  onClick={(e) => {
                    e.preventDefault();
                    navigate(item.path);
                    if (onClose) onClose();
                  }}
                  className={`nav-item flex items-center justify-between p-4 rounded-xl transition-all duration-300 group ${
                    isActive(item.path)
                      ? 'active'
                      : 'text-white/80 hover:text-white'
                  }`}
                >
                  <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`transition-all duration-300 ${
                      isActive(item.path)
                        ? 'text-purple-300 scale-110 drop-shadow-lg'
                        : 'text-white/70 group-hover:text-purple-300 group-hover:scale-105'
                    }`}>
                      {item.icon}
                    </div>
                    <span className={`text-sm font-medium transition-all duration-300 ${isRTL ? 'text-right' : 'text-left'} ${
                      isActive(item.path) ? 'text-white font-semibold' : 'group-hover:text-white'
                    }`}>{item.name}</span>
                  </div>
                  {getRiskBadge(item.riskLevel)}
                </a>
              ))}
            </div>
          )}

          {/* Content & Resources */}
          {categorizedItems.content.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-xs font-semibold text-white/50 uppercase tracking-wider px-2">
                {t('navigation.content', 'Content & Resources')}
              </h3>
              {categorizedItems.content.map(item => (
                <a
                  key={item.id}
                  href={item.path}
                  onClick={(e) => {
                    e.preventDefault();
                    navigate(item.path);
                    if (onClose) onClose();
                  }}
                  className={`nav-item flex items-center justify-between p-3 rounded-xl transition-all duration-300 group ${
                    isActive(item.path)
                      ? 'active'
                      : 'text-white/70 hover:text-white'
                  }`}
                >
                  <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`transition-all duration-300 ${
                      isActive(item.path)
                        ? 'text-purple-300 scale-110 drop-shadow-lg'
                        : 'text-white/60 group-hover:text-purple-300 group-hover:scale-105'
                    }`}>
                      {item.icon}
                    </div>
                    <span className={`text-sm font-medium transition-all duration-300 ${isRTL ? 'text-right' : 'text-left'} ${
                      isActive(item.path) ? 'text-white font-semibold' : 'group-hover:text-white'
                    }`}>{item.name}</span>
                  </div>
                  {getRiskBadge(item.riskLevel)}
                </a>
              ))}
            </div>
          )}

          {/* AI Features */}
          {categorizedItems.ai.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-xs font-semibold text-white/50 uppercase tracking-wider px-2">
                {t('navigation.ai', 'AI Features')}
              </h3>
              {categorizedItems.ai.map(item => (
                <a
                  key={item.id}
                  href={item.path}
                  onClick={(e) => {
                    e.preventDefault();
                    navigate(item.path);
                    if (onClose) onClose();
                  }}
                  className={`nav-item flex items-center justify-between p-3 rounded-xl transition-all duration-300 group ${
                    isActive(item.path)
                      ? 'active'
                      : 'text-white/70 hover:text-white'
                  }`}
                >
                  <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`transition-all duration-300 ${
                      isActive(item.path)
                        ? 'text-purple-300 scale-110 drop-shadow-lg'
                        : 'text-white/60 group-hover:text-purple-300 group-hover:scale-105'
                    }`}>
                      {item.icon}
                    </div>
                    <span className={`text-sm font-medium ${isRTL ? 'text-right' : 'text-left'}`}>{item.name}</span>
                  </div>
                  {getRiskBadge(item.riskLevel)}
                </a>
              ))}
            </div>
          )}

          {/* System & Admin */}
          {categorizedItems.system.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-xs font-semibold text-white/50 uppercase tracking-wider px-2">
                {t('navigation.system', 'System & Admin')}
              </h3>
              {categorizedItems.system.map(item => (
                <a
                  key={item.id}
                  href={item.path}
                  onClick={(e) => {
                    e.preventDefault();
                    navigate(item.path);
                    if (onClose) onClose();
                  }}
                  className={`nav-item flex items-center justify-between p-3 rounded-xl transition-all duration-300 group ${
                    isActive(item.path)
                      ? 'active'
                      : 'text-white/70 hover:text-white'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <div className={`transition-all duration-300 ${
                      isActive(item.path)
                        ? 'text-purple-300 scale-110 drop-shadow-lg'
                        : 'text-white/60 group-hover:text-purple-300 group-hover:scale-105'
                    }`}>
                      {item.icon}
                    </div>
                    <span className="text-sm font-medium">{item.name}</span>
                  </div>
                  {getRiskBadge(item.riskLevel)}
                </a>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* User Profile & Logout */}
      <div className="border-t border-white/20 p-4">
        <button
          onClick={handleLogout}
          className={`flex items-center justify-center gap-3 w-full p-3 bg-gradient-to-r from-red-600/20 to-red-700/20 hover:from-red-600/30 hover:to-red-700/30 text-red-300 hover:text-red-200 rounded-xl transition-all duration-300 group border border-red-500/30 hover:border-red-400/50 hover:shadow-lg hover:shadow-red-500/20 ${isRTL ? 'flex-row-reverse' : ''}`}
        >
          <LogOut className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
          <span className={`text-sm font-medium ${isRTL ? 'text-right' : 'text-left'}`}>{t('auth.logout', 'Logout')}</span>
        </button>
      </div>
    </div>
  );

  if (variant === 'mobile') {
    return (
      <>
        {/* Backdrop overlay */}
        <div
          className={`fixed inset-0 z-40 bg-black/60 backdrop-blur-sm transition-opacity duration-300 ${
            isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
          }`}
          onClick={onClose}
          aria-hidden="true"
        />
        {/* Sidebar panel */}
        <div className={`fixed top-0 ${isRTL ? 'right-0' : 'left-0'} z-50 h-full w-80 max-w-xs transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : (isRTL ? 'translate-x-full' : '-translate-x-full')
        }`}>
          <div className="flex flex-col h-full shadow-2xl">
            {sidebarContent}
          </div>
        </div>
      </>
    );
  }

  return (
    <div className={`hidden lg:flex lg:flex-shrink-0 transition-all duration-300 ease-in-out fixed ${isRTL ? 'right-0' : 'left-0'} top-0 h-full z-40 ${
      isCollapsed ? 'w-16' : 'w-80'
    }`}>
      <div className={`flex flex-col shadow-2xl h-full ${isCollapsed ? 'w-16' : 'w-80'}`}>
        {/* Toggle button for desktop */}
        <button
          onClick={onToggle}
          className={`absolute ${isRTL ? 'left-4' : 'right-4'} top-4 z-10 p-2 glass-light border border-white/20 hover:bg-white/10 rounded-lg transition-colors ${
            isCollapsed ? 'opacity-100' : 'opacity-0 hover:opacity-100'
          }`}
          aria-label={isCollapsed ? t('common.expand') : t('common.collapse')}
        >
          {isCollapsed ? (
            isRTL ? <ChevronLeft className="w-4 h-4 text-white" /> : <ChevronRight className="w-4 h-4 text-white" />
          ) : (
            isRTL ? <ChevronRight className="w-4 h-4 text-white" /> : <ChevronLeft className="w-4 h-4 text-white" />
          )}
        </button>
        {isCollapsed ? (
          // Collapsed sidebar content
          <div className={`flex flex-col h-full universal-sidebar ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
            {/* Collapsed header */}
            <div className="sidebar-header flex items-center justify-center p-4">
              <div className="app-logo w-12 h-12 rounded-full flex items-center justify-center shadow-lg">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
            </div>

            {/* Collapsed navigation - All items in simple list */}
            <div className="flex-1 overflow-y-auto p-2 custom-scrollbar">
              <div className="space-y-1">
                {/* Main items first */}
                {categorizedItems.main.map(item => (
                  <a
                    key={item.id}
                    href={item.path}
                    onClick={(e) => {
                      e.preventDefault();
                      navigate(item.path);
                    }}
                    className={`nav-item flex items-center justify-center p-3 rounded-xl transition-all duration-300 group ${
                      isActive(item.path)
                        ? 'active'
                        : 'text-white/70 hover:text-white'
                    }`}
                    title={item.name}
                  >
                    <div className={`transition-all duration-300 ${
                      isActive(item.path)
                        ? 'text-purple-300 scale-110 drop-shadow-lg'
                        : 'text-white/60 group-hover:text-purple-300 group-hover:scale-105'
                    }`}>
                      {item.icon}
                    </div>
                  </a>
                ))}

                {/* Separator for content items */}
                {categorizedItems.content.length > 0 && (
                  <div className="h-px bg-white/20 my-2"></div>
                )}

                {/* Content items */}
                {categorizedItems.content.map(item => (
                  <a
                    key={item.id}
                    href={item.path}
                    onClick={(e) => {
                      e.preventDefault();
                      navigate(item.path);
                    }}
                    className={`nav-item flex items-center justify-center p-3 rounded-xl transition-all duration-300 group ${
                      isActive(item.path)
                        ? 'active'
                        : 'text-white/70 hover:text-white'
                    }`}
                    title={item.name}
                  >
                    <div className={`transition-all duration-300 ${
                      isActive(item.path)
                        ? 'text-purple-300 scale-110 drop-shadow-lg'
                        : 'text-white/60 group-hover:text-purple-300 group-hover:scale-105'
                    }`}>
                      {item.icon}
                    </div>
                  </a>
                ))}

                {/* AI and System items */}
                {(categorizedItems.ai.length > 0 || categorizedItems.system.length > 0) && (
                  <div className="h-px bg-white/20 my-2"></div>
                )}

                {categorizedItems.ai.map(item => (
                  <a
                    key={item.id}
                    href={item.path}
                    onClick={(e) => {
                      e.preventDefault();
                      navigate(item.path);
                    }}
                    className={`nav-item flex items-center justify-center p-3 rounded-xl transition-all duration-300 group ${
                      isActive(item.path)
                        ? 'active'
                        : 'text-white/70 hover:text-white'
                    }`}
                    title={item.name}
                  >
                    <div className={`transition-all duration-300 ${
                      isActive(item.path)
                        ? 'text-purple-300 scale-110 drop-shadow-lg'
                        : 'text-white/60 group-hover:text-purple-300 group-hover:scale-105'
                    }`}>
                      {item.icon}
                    </div>
                  </a>
                ))}

                {categorizedItems.system.map(item => (
                  <a
                    key={item.id}
                    href={item.path}
                    onClick={(e) => {
                      e.preventDefault();
                      navigate(item.path);
                    }}
                    className={`nav-item flex items-center justify-center p-3 rounded-xl transition-all duration-300 group ${
                      isActive(item.path)
                        ? 'active'
                        : 'text-white/70 hover:text-white'
                    }`}
                    title={item.name}
                  >
                    <div className={`transition-all duration-300 ${
                      isActive(item.path)
                        ? 'text-purple-300 scale-110 drop-shadow-lg'
                        : 'text-white/60 group-hover:text-purple-300 group-hover:scale-105'
                    }`}>
                      {item.icon}
                    </div>
                  </a>
                ))}
              </div>
            </div>

            {/* Collapsed logout */}
            <div className="p-2 border-t border-white/20">
              <button
                onClick={handleLogout}
                className="w-full flex items-center justify-center p-3 text-white/70 hover:bg-red-500/20 hover:text-red-300 rounded-xl transition-all duration-200 glass-light border border-white/20 hover:border-red-500/50"
                title={t('auth.logout')}
              >
                <LogOut className="w-5 h-5" />
              </button>
            </div>
          </div>
        ) : (
          sidebarContent
        )}
      </div>
    </div>
  );
};

export default UniversalSidebar;
