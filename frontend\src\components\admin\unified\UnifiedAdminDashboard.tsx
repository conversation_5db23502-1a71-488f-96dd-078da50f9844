/**
 * Unified Admin Dashboard
 * Single dashboard that adapts based on user role (Admin vs Super Admin)
 * Eliminates duplication between admin and super admin dashboards
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Shield, Users, BarChart3, Settings, Activity,
  Server, Database, Lock, Eye, AlertTriangle,
  CheckCircle, TrendingUp, Cpu, HardDrive, Monitor
} from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { unifiedAdminAPI, UnifiedDashboardStats, AdminCapability } from '../../../services/unifiedAdminApi';

interface UnifiedAdminDashboardProps {
  className?: string;
}

const UnifiedAdminDashboard: React.FC<UnifiedAdminDashboardProps> = ({ className = '' }) => {
  const { t } = useTranslation();
  const [stats, setStats] = useState<UnifiedDashboardStats | null>(null);
  const [capabilities, setCapabilities] = useState<AdminCapability[]>([]);
  const [adminLevel, setAdminLevel] = useState<'admin' | 'super_admin' | 'none'>('none');
  const [systemHealth, setSystemHealth] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDashboardData();
    
    // Auto-refresh every 5 minutes
    const interval = setInterval(loadDashboardData, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load all data in parallel
      const [dashboardStats, userCapabilities, userAdminLevel] = await Promise.all([
        unifiedAdminAPI.getDashboardStats(),
        unifiedAdminAPI.getCapabilities(),
        unifiedAdminAPI.getAdminLevel()
      ]);

      setStats(dashboardStats);
      setCapabilities(userCapabilities);
      setAdminLevel(userAdminLevel);

      // Load system health if Super Admin
      if (userAdminLevel === 'super_admin') {
        const health = await unifiedAdminAPI.getSystemHealth();
        setSystemHealth(health);
      }

    } catch (err) {
      console.error('Error loading dashboard data:', err);
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getAdminLevelBadge = () => {
    switch (adminLevel) {
      case 'super_admin':
        return (
          <div className="flex items-center gap-2 px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">
            <Shield className="w-4 h-4" />
            Super Admin
          </div>
        );
      case 'admin':
        return (
          <div className="flex items-center gap-2 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
            <Users className="w-4 h-4" />
            Admin
          </div>
        );
      default:
        return null;
    }
  };

  const renderSystemHealth = () => {
    if (adminLevel !== 'super_admin' || !systemHealth) return null;

    return (
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Activity className="w-5 h-5 text-purple-600" />
          System Health
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <Cpu className="w-6 h-6 text-blue-500" />
            <div>
              <div className="text-sm text-gray-600">CPU Usage</div>
              <div className="text-lg font-semibold">{systemHealth.cpu?.usage_percent || 0}%</div>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <Monitor className="w-6 h-6 text-green-500" />
            <div>
              <div className="text-sm text-gray-600">Memory</div>
              <div className="text-lg font-semibold">{systemHealth.memory?.percent || 0}%</div>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <HardDrive className="w-6 h-6 text-orange-500" />
            <div>
              <div className="text-sm text-gray-600">Disk Usage</div>
              <div className="text-lg font-semibold">{systemHealth.disk?.percent || 0}%</div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderStats = () => {
    if (!stats) return null;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        {/* User Stats */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center gap-3 mb-4">
            <Users className="w-8 h-8 text-blue-600" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Users</h3>
              <p className="text-sm text-gray-600">Platform members</p>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">Total</span>
              <span className="font-semibold">{stats.users.total_users}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Active</span>
              <span className="font-semibold text-green-600">{stats.users.active_users}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">New</span>
              <span className="font-semibold text-blue-600">{stats.users.new_users}</span>
            </div>
            {adminLevel === 'super_admin' && stats.users.staff_users && (
              <div className="flex justify-between">
                <span className="text-gray-600">Staff</span>
                <span className="font-semibold text-purple-600">{stats.users.staff_users}</span>
              </div>
            )}
          </div>
        </div>

        {/* Content Stats */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center gap-3 mb-4">
            <BarChart3 className="w-8 h-8 text-green-600" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Content</h3>
              <p className="text-sm text-gray-600">Platform content</p>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">Posts</span>
              <span className="font-semibold">{stats.content.total_posts}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Events</span>
              <span className="font-semibold">{stats.content.total_events}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Resources</span>
              <span className="font-semibold">{stats.content.total_resources}</span>
            </div>
          </div>
        </div>

        {/* System Stats (Super Admin only) */}
        {adminLevel === 'super_admin' && stats.system && (
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center gap-3 mb-4">
              <Server className="w-8 h-8 text-purple-600" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">System</h3>
                <p className="text-sm text-gray-600">Server status</p>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Status</span>
                <div className="flex items-center gap-1">
                  {stats.system.status === 'healthy' ? (
                    <CheckCircle className="w-4 h-4 text-green-500" />
                  ) : (
                    <AlertTriangle className="w-4 h-4 text-yellow-500" />
                  )}
                  <span className={`text-sm font-medium ${
                    stats.system.status === 'healthy' ? 'text-green-600' : 'text-yellow-600'
                  }`}>
                    {stats.system.status}
                  </span>
                </div>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">CPU</span>
                <span className="font-semibold">{stats.system.cpu_usage}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Memory</span>
                <span className="font-semibold">{stats.system.memory_usage}%</span>
              </div>
            </div>
          </div>
        )}

        {/* Security Stats (Super Admin only) */}
        {adminLevel === 'super_admin' && stats.security && (
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center gap-3 mb-4">
              <Lock className="w-8 h-8 text-red-600" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Security</h3>
                <p className="text-sm text-gray-600">Security status</p>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Failed Logins</span>
                <span className="font-semibold text-red-600">{stats.security.failed_logins}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Active Sessions</span>
                <span className="font-semibold">{stats.security.active_sessions}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Alerts</span>
                <span className="font-semibold text-orange-600">{stats.security.security_alerts}</span>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <DashboardLayout currentPage="admin">
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout currentPage="admin">
      <div className={`space-y-6 ${className}`}>
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {adminLevel === 'super_admin' ? 'Super Admin Dashboard' : 'Admin Dashboard'}
            </h1>
            <p className="text-gray-600 mt-1">
              {adminLevel === 'super_admin' 
                ? 'Complete system control and monitoring'
                : 'Platform administration and management'
              }
            </p>
          </div>
          {getAdminLevelBadge()}
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-red-500" />
              <span className="text-red-700">{error}</span>
            </div>
          </div>
        )}

        {/* System Health (Super Admin only) */}
        {renderSystemHealth()}

        {/* Dashboard Stats */}
        {renderStats()}

        {/* Capabilities Grid */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Available Capabilities</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {capabilities.map((capability) => (
              <div key={capability.id} className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                <h4 className="font-medium text-gray-900 mb-2">{capability.name}</h4>
                <p className="text-sm text-gray-600 mb-3">{capability.description}</p>
                <div className="flex items-center justify-between">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    capability.permission_level === 'super_admin' 
                      ? 'bg-red-100 text-red-800' 
                      : 'bg-blue-100 text-blue-800'
                  }`}>
                    {capability.permission_level === 'super_admin' ? 'Super Admin' : 'Admin'}
                  </span>
                  {capability.usage_count && (
                    <span className="text-sm text-gray-500">
                      Used {capability.usage_count} times
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default UnifiedAdminDashboard;
