import React, { useState, useEffect } from 'react';
import { Globe, Key, Activity, TrendingUp, Clock, AlertCircle, CheckCircle, Zap, Database, Server } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

interface ApiEndpoint {
  id: string;
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  description: string;
  status: 'active' | 'deprecated' | 'maintenance';
  version: string;
  requests_24h: number;
  avg_response_time: number;
  error_rate: number;
  last_called: string;
}

interface ApiKey {
  id: string;
  name: string;
  key: string;
  user: string;
  permissions: string[];
  requests_today: number;
  rate_limit: number;
  created_at: string;
  last_used: string;
  status: 'active' | 'suspended' | 'expired';
}

interface ApiMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  change: number;
  status: 'good' | 'warning' | 'critical';
  icon: React.ReactNode;
  color: string;
}

const ApiManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  // State management
  const [endpoints, setEndpoints] = useState<ApiEndpoint[]>([]);
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [metrics, setMetrics] = useState<ApiMetric[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'endpoints' | 'keys'>('overview');

  // Fetch real API data instead of using mock data
  useEffect(() => {
    const fetchApiData = async () => {
      try {
        setLoading(true);

        // Fetch real API metrics
        const metricsResponse = await apiManagementApi.getMetrics();
        const metricsData: ApiMetric[] = metricsResponse.data.map((metric: any) => ({
          id: metric.id,
          name: metric.name,
          value: metric.value,
          unit: metric.unit || '',
          change: metric.change_percentage || 0,
          status: metric.status || 'good',
          icon: getMetricIcon(metric.type),
          color: getMetricColor(metric.type)
        }));

        // Fetch real API endpoints
        const endpointsResponse = await apiManagementApi.getEndpoints();
        const endpointsData: ApiEndpoint[] = endpointsResponse.data;

        // Fetch real API keys
        const apiKeysResponse = await apiManagementApi.getApiKeys();
        const apiKeysData: ApiKey[] = apiKeysResponse.data;

        setMetrics(metricsData);
        setEndpoints(endpointsData);
        setApiKeys(apiKeysData);
        setLoading(false);
      } catch (error) {
        console.error('Failed to fetch API data:', error);
        setMetrics([]);
        setEndpoints([]);
        setApiKeys([]);
        setLoading(false);
      }
    };

    fetchApiData();
  }, []);

  // Helper functions for metric icons and colors
  const getMetricIcon = (type: string) => {
    switch (type) {
      case 'requests': return <Globe size={20} />;
      case 'response_time': return <Clock size={20} />;
      case 'error_rate': return <AlertCircle size={20} />;
      case 'api_keys': return <Key size={20} />;
      default: return <Globe size={20} />;
    }
  };

  const getMetricColor = (type: string) => {
    switch (type) {
      case 'requests': return 'text-blue-400';
      case 'response_time': return 'text-green-400';
      case 'error_rate': return 'text-yellow-400';
      case 'api_keys': return 'text-purple-400';
      default: return 'text-gray-400';
    }
  };

  // Get method color
  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET':
        return 'bg-green-500/20 text-green-300';
      case 'POST':
        return 'bg-blue-500/20 text-blue-300';
      case 'PUT':
        return 'bg-yellow-500/20 text-yellow-300';
      case 'DELETE':
        return 'bg-red-500/20 text-red-300';
      case 'PATCH':
        return 'bg-purple-500/20 text-purple-300';
      default:
        return 'bg-gray-500/20 text-gray-300';
    }
  };

  // Format number
  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  // Tabs
  const tabs = [
    { id: 'overview', label: t('api.overview', 'Overview'), icon: <Activity size={16} /> },
    { id: 'endpoints', label: t('api.endpoints', 'Endpoints'), icon: <Globe size={16} /> },
    { id: 'keys', label: t('api.keys', 'API Keys'), icon: <Key size={16} /> }
  ];

  return (
    <DashboardLayout currentPage="api">
      {/* Header */}
      <div className={`mb-8 ${isRTL ? "text-right" : ""}`}>
        <h1 className="text-2xl font-bold text-white">{t('admin.api.management', 'API Management')}</h1>
        <div className="text-gray-400 mt-1">{t('admin.api.description', 'Monitor and manage API endpoints and access')}</div>
      </div>

      {/* Tabs */}
      <div className="mb-8">
        <div className={`flex space-x-1 bg-indigo-900/30 p-1 rounded-lg ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                activeTab === tab.id
                  ? 'bg-purple-600 text-white shadow-lg'
                  : 'text-gray-300 hover:text-white hover:bg-indigo-800/50'
              } ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}
            >
              {tab.icon}
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div>
          {/* API Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {metrics.map((metric) => (
              <div key={metric.id} className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
                <div className={`flex items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={metric.color}>
                    {metric.icon}
                  </div>
                  <div className={`flex items-center space-x-1 text-sm ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                    <TrendingUp size={14} className={metric.change >= 0 ? 'text-green-400' : 'text-red-400'} />
                    <span className={metric.change >= 0 ? 'text-green-400' : 'text-red-400'}>
                      {metric.change > 0 ? '+' : ''}{metric.change.toFixed(1)}%
                    </span>
                  </div>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white mb-1">
                    {formatNumber(metric.value)}{metric.unit}
                  </h3>
                  <p className="text-gray-400 text-sm">{metric.name}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Top Endpoints */}
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
            <h3 className="text-lg font-semibold text-white mb-6">{t('api.top.endpoints', 'Top API Endpoints')}</h3>
            
            <div className="space-y-3">
              {endpoints.slice(0, 5).map((endpoint) => (
                <div key={endpoint.id} className="p-3 bg-indigo-800/30 rounded-lg">
                  <div className={`flex items-center justify-between mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getMethodColor(endpoint.method)}`}>
                        {endpoint.method}
                      </span>
                      <span className="text-white font-mono text-sm">{endpoint.path}</span>
                    </div>
                    <span className="text-green-400 text-sm">{endpoint.status}</span>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-400">{t('api.requests', 'Requests')}: </span>
                      <span className="text-white">{formatNumber(endpoint.requests_24h)}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">{t('api.avg.time', 'Avg Time')}: </span>
                      <span className="text-white">{endpoint.avg_response_time}ms</span>
                    </div>
                    <div>
                      <span className="text-gray-400">{t('api.error.rate', 'Error Rate')}: </span>
                      <span className={endpoint.error_rate > 5 ? 'text-red-400' : 'text-green-400'}>
                        {endpoint.error_rate}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'keys' && (
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
          <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
            <h3 className="text-lg font-semibold text-white">{t('api.keys.management', 'API Keys Management')}</h3>
            <button className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg font-medium transition-colors">
              {t('api.create.key', 'Create API Key')}
            </button>
          </div>
          
          <div className="space-y-4">
            {apiKeys.map((apiKey) => (
              <div key={apiKey.id} className="p-4 bg-indigo-800/30 rounded-lg">
                <div className={`flex items-center justify-between mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div>
                    <h4 className="text-white font-medium">{apiKey.name}</h4>
                    <p className="text-gray-400 text-sm">{apiKey.user}</p>
                  </div>
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-300">
                    {apiKey.status}
                  </span>
                </div>
                
                <div className="mb-3">
                  <div className="text-xs text-gray-400 mb-1">{t('api.key', 'API Key')}</div>
                  <div className="font-mono text-sm text-white bg-indigo-900/50 p-2 rounded">
                    {apiKey.key}
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                  <div>
                    <span className="text-gray-400">{t('api.requests.today', 'Requests Today')}: </span>
                    <span className="text-white">{formatNumber(apiKey.requests_today)}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">{t('api.rate.limit', 'Rate Limit')}: </span>
                    <span className="text-white">{formatNumber(apiKey.rate_limit)}/day</span>
                  </div>
                </div>
                
                <div className="mb-3">
                  <div className="text-xs text-gray-400 mb-1">{t('api.permissions', 'Permissions')}</div>
                  <div className="flex flex-wrap gap-1">
                    {apiKey.permissions.map((permission, index) => (
                      <span key={index} className="px-2 py-1 bg-purple-500/20 text-purple-300 rounded text-xs">
                        {permission}
                      </span>
                    ))}
                  </div>
                </div>
                
                {/* Usage Bar */}
                <div className="mb-3">
                  <div className={`flex justify-between text-xs mb-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <span className="text-gray-400">{t('api.usage.today', 'Usage Today')}</span>
                    <span className="text-white">{((apiKey.requests_today / apiKey.rate_limit) * 100).toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${
                        (apiKey.requests_today / apiKey.rate_limit) > 0.9 ? 'bg-red-500' :
                        (apiKey.requests_today / apiKey.rate_limit) > 0.7 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${Math.min((apiKey.requests_today / apiKey.rate_limit) * 100, 100)}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default ApiManagement;
