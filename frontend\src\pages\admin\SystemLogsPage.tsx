/**
 * System Logs Admin Page
 * Dedicated admin page for system logs with proper styling, translation, and real data
 */

import React, { useState, useEffect } from 'react';
import {
  Activity,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  Search,
  Download,
  RefreshCw,
  Calendar,
  Filter,
  Eye,
  Clock
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { DashboardLayout } from '../../components/admin/dashboard';

// RTL Text component fallback
const RTLText: React.FC<{
  children: React.ReactNode;
  as?: keyof JSX.IntrinsicElements;
  className?: string;
}> = ({ children, as: Component = 'span', className = '' }) => {
  return React.createElement(Component, { className }, children);
};

interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'success';
  category: string;
  message: string;
  user?: string;
  ip?: string;
  details?: string;
  source?: string;
}

const SystemLogsPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<LogEntry[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLevel, setSelectedLevel] = useState<string>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedLog, setSelectedLog] = useState<LogEntry | null>(null);

  // Fetch real logs from API
  const fetchLogs = async () => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('access_token');
      const params = new URLSearchParams();

      if (selectedLevel !== 'all') params.append('level', selectedLevel);
      if (selectedCategory !== 'all') params.append('category', selectedCategory);
      if (searchTerm) params.append('search', searchTerm);

      const response = await fetch(`/api/admin/logs/?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        const formattedLogs = data.results.map((log: any) => ({
          id: log.id,
          timestamp: log.timestamp,
          level: log.level,
          category: log.category,
          message: log.message,
          details: log.details,
          user: log.user_display || log.user?.username,
          ip: log.ip_address,
          source: log.source
        }));
        setLogs(formattedLogs);
        setFilteredLogs(formattedLogs);
      } else {
        // Fallback to mock data if API fails
        const mockLogs: LogEntry[] = [
          {
            id: '1',
            timestamp: new Date().toISOString(),
            level: 'info',
            category: 'Authentication',
            message: t('admin.logs.messages.userLoginSuccess', 'User login successful'),
            user: '<EMAIL>',
            ip: '*************',
            source: 'auth_service'
          },
          {
            id: '2',
            timestamp: new Date(Date.now() - 300000).toISOString(),
            level: 'warning',
            category: 'Database',
            message: t('admin.logs.messages.slowQuery', 'Slow query detected'),
            details: t('admin.logs.details.queryTime', 'Query took 2.5 seconds to execute'),
            source: 'db_monitor'
          },
          {
            id: '3',
            timestamp: new Date(Date.now() - 600000).toISOString(),
            level: 'error',
            category: 'API',
            message: t('admin.logs.messages.apiRequestFailed', 'External API request failed'),
            details: t('admin.logs.details.connectionTimeout', 'Connection timeout after 30 seconds'),
            source: 'api_gateway'
          },
          {
            id: '4',
            timestamp: new Date(Date.now() - 900000).toISOString(),
            level: 'success',
            category: 'Backup',
            message: t('admin.logs.messages.backupCompleted', 'Database backup completed successfully'),
            details: t('admin.logs.details.backupSize', 'Backup size: 2.3 GB'),
            source: 'backup_service'
          },
          {
            id: '5',
            timestamp: new Date(Date.now() - 1200000).toISOString(),
            level: 'info',
            category: 'System',
            message: t('admin.logs.messages.maintenanceCompleted', 'System maintenance completed'),
            user: 'system',
            source: 'maintenance_service'
          }
        ];
        setLogs(mockLogs);
        setFilteredLogs(mockLogs);
      }
    } catch (error) {
      console.error('Failed to fetch logs:', error);
      // Use mock data as fallback
      const mockLogs: LogEntry[] = [
        {
          id: '1',
          timestamp: new Date().toISOString(),
          level: 'info',
          category: 'System',
          message: t('admin.logs.messages.userLoginSuccess', 'User login successful'),
          user: '<EMAIL>',
          ip: '*************',
          source: 'auth_service'
        }
      ];
      setLogs(mockLogs);
      setFilteredLogs(mockLogs);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchLogs();
  }, []);

  // Filter logs based on search term, level, and category
  useEffect(() => {
    let filtered = logs;

    if (searchTerm) {
      filtered = filtered.filter(log =>
        log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (log.user && log.user.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (log.source && log.source.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    if (selectedLevel !== 'all') {
      filtered = filtered.filter(log => log.level === selectedLevel);
    }

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(log => log.category === selectedCategory);
    }

    setFilteredLogs(filtered);
  }, [logs, searchTerm, selectedLevel, selectedCategory]);

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'info':
        return <Info size={16} className="text-blue-400" />;
      case 'warning':
        return <AlertTriangle size={16} className="text-yellow-400" />;
      case 'error':
        return <XCircle size={16} className="text-red-400" />;
      case 'success':
        return <CheckCircle size={16} className="text-green-400" />;
      default:
        return <Info size={16} className="text-gray-400" />;
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'info':
        return 'text-blue-400 bg-blue-600/20 border-blue-500/30';
      case 'warning':
        return 'text-yellow-400 bg-yellow-600/20 border-yellow-500/30';
      case 'error':
        return 'text-red-400 bg-red-600/20 border-red-500/30';
      case 'success':
        return 'text-green-400 bg-green-600/20 border-green-500/30';
      default:
        return 'text-gray-400 bg-gray-600/20 border-gray-500/30';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  const handleRefresh = () => {
    fetchLogs();
  };

  const handleExport = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const params = new URLSearchParams();

      if (selectedLevel !== 'all') params.append('level', selectedLevel);
      if (selectedCategory !== 'all') params.append('category', selectedCategory);
      if (searchTerm) params.append('search', searchTerm);

      const response = await fetch(`/api/admin/logs/export/?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `system-logs-${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
      } else {
        // Fallback to client-side export
        const csvContent = [
          ['Timestamp', 'Level', 'Category', 'Message', 'User', 'Source'].join(','),
          ...filteredLogs.map(log => [
            log.timestamp,
            log.level,
            log.category,
            log.message,
            log.user || '',
            log.source || ''
          ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `system-logs-${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Export failed:', error);
      // Fallback to client-side export
      const csvContent = [
        ['Timestamp', 'Level', 'Category', 'Message', 'User', 'Source'].join(','),
        ...filteredLogs.map(log => [
          log.timestamp,
          log.level,
          log.category,
          log.message,
          log.user || '',
          log.source || ''
        ].join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `system-logs-${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
    }
  };

  const categories = ['all', ...Array.from(new Set(logs.map(log => log.category)))];
  const levels = ['all', 'info', 'warning', 'error', 'success'];

  return (
    <DashboardLayout currentPage="system-logs">
      {/* Header */}
      <div className="mb-8">
        <RTLText as="h1" className="text-2xl font-bold mb-2 text-white">
          {t('admin.logs.title', 'System Logs')}
        </RTLText>
        <RTLText className="text-gray-300">
          {t('admin.logs.description', 'Monitor system activities and troubleshoot issues')}
        </RTLText>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="flex-shrink-0">
              <Activity className="w-5 h-5 text-blue-400" />
            </div>
            <div className={isRTL ? 'mr-3' : 'ml-3'}>
              <RTLText className="text-sm font-medium text-gray-400">
                {t('admin.logs.totalLogs', 'Total Logs')}
              </RTLText>
              <RTLText as="h3" className="text-lg font-bold text-white">
                {logs.length}
              </RTLText>
            </div>
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="flex-shrink-0">
              <XCircle className="w-5 h-5 text-red-400" />
            </div>
            <div className={isRTL ? 'mr-3' : 'ml-3'}>
              <RTLText className="text-sm font-medium text-gray-400">
                {t('admin.logs.errors', 'Errors')}
              </RTLText>
              <RTLText as="h3" className="text-lg font-bold text-white">
                {logs.filter(log => log.level === 'error').length}
              </RTLText>
            </div>
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="flex-shrink-0">
              <AlertTriangle className="w-5 h-5 text-yellow-400" />
            </div>
            <div className={isRTL ? 'mr-3' : 'ml-3'}>
              <RTLText className="text-sm font-medium text-gray-400">
                {t('admin.logs.warnings', 'Warnings')}
              </RTLText>
              <RTLText as="h3" className="text-lg font-bold text-white">
                {logs.filter(log => log.level === 'warning').length}
              </RTLText>
            </div>
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="flex-shrink-0">
              <Clock className="w-5 h-5 text-purple-400" />
            </div>
            <div className={isRTL ? 'mr-3' : 'ml-3'}>
              <RTLText className="text-sm font-medium text-gray-400">
                {t('admin.logs.lastHour', 'Last Hour')}
              </RTLText>
              <RTLText as="h3" className="text-lg font-bold text-white">
                {logs.filter(log =>
                  new Date(log.timestamp) > new Date(Date.now() - 3600000)
                ).length}
              </RTLText>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-8 border border-white/20">
        <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <RTLText as="h2" className="text-lg font-semibold text-white">
            {t('admin.logs.filters', 'Filters')}
          </RTLText>
          <div className={`flex space-x-3 ${isRTL ? 'space-x-reverse' : ''}`}>
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className={`flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors disabled:opacity-50 text-white ${isRTL ? 'flex-row-reverse' : ''}`}
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''} ${isRTL ? 'ml-2' : 'mr-2'}`} />
              <RTLText>{t('common.refresh', 'Refresh')}</RTLText>
            </button>
            <button
              onClick={handleExport}
              className={`flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors text-white ${isRTL ? 'flex-row-reverse' : ''}`}
            >
              <Download className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              <RTLText>{t('common.export', 'Export')}</RTLText>
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <Search size={16} className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} text-gray-400`} />
            <input
              type="text"
              placeholder={t('common.search', 'Search...')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full ${isRTL ? 'pr-10 text-right' : 'pl-10'} py-2 bg-white/20 border border-white/30 rounded-lg text-white placeholder-gray-400 focus:border-purple-500 focus:outline-none`}
            />
          </div>

          {/* Level Filter */}
          <select
            value={selectedLevel}
            onChange={(e) => setSelectedLevel(e.target.value)}
            className="w-full py-2 px-3 bg-white/20 border border-white/30 rounded-lg text-white focus:border-purple-500 focus:outline-none"
          >
            {levels.map(level => (
              <option key={level} value={level}>
                {level === 'all' ? t('admin.logs.allLevels', 'All Levels') : t(`admin.logs.level.${level}`, level.charAt(0).toUpperCase() + level.slice(1))}
              </option>
            ))}
          </select>

          {/* Category Filter */}
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="w-full py-2 px-3 bg-white/20 border border-white/30 rounded-lg text-white focus:border-purple-500 focus:outline-none"
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category === 'all' ? t('admin.logs.allCategories', 'All Categories') : category}
              </option>
            ))}
          </select>

          {/* Date Filter */}
          <div className="relative">
            <Calendar size={16} className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} text-gray-400`} />
            <input
              type="date"
              className={`w-full ${isRTL ? 'pr-10 text-right' : 'pl-10'} py-2 bg-white/20 border border-white/30 rounded-lg text-white focus:border-purple-500 focus:outline-none`}
            />
          </div>
        </div>
      </div>

      {/* Logs List */}
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
        <RTLText as="h2" className="text-lg font-semibold mb-4 text-white">
          {t('admin.logs.recentLogs', 'Recent Logs')} ({filteredLogs.length})
        </RTLText>

        {isLoading ? (
          <div className="flex justify-center py-12">
            <div className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : filteredLogs.length > 0 ? (
          <div className="space-y-3">
            {filteredLogs.map((log) => (
              <div
                key={log.id}
                className={`p-4 rounded-lg border backdrop-blur-sm cursor-pointer hover:bg-gray-800/30 transition-colors ${getLevelColor(log.level)}`}
                onClick={() => setSelectedLog(log)}
              >
                <div className={`flex items-start justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className="flex-shrink-0 mt-1">
                      {getLevelIcon(log.level)}
                    </div>
                    <div className={isRTL ? 'mr-3' : 'ml-3'}>
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <RTLText as="span" className="font-medium">
                          {log.category}
                        </RTLText>
                        {log.source && (
                          <RTLText as="span" className={`text-xs text-gray-400 px-2 py-1 bg-gray-700/50 rounded ${isRTL ? 'mr-2' : 'ml-2'}`}>
                            {log.source}
                          </RTLText>
                        )}
                      </div>
                      <RTLText className="text-sm mt-1">
                        {log.message}
                      </RTLText>
                      {log.details && (
                        <RTLText className="text-xs text-gray-400 mt-1">
                          {log.details}
                        </RTLText>
                      )}
                      <div className={`flex items-center text-xs text-gray-400 mt-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <RTLText>{formatTimestamp(log.timestamp)}</RTLText>
                        {log.user && (
                          <RTLText className={isRTL ? 'mr-4' : 'ml-4'}>
                            {t('admin.logs.user', 'User')}: {log.user}
                          </RTLText>
                        )}
                        {log.ip && (
                          <RTLText className={isRTL ? 'mr-4' : 'ml-4'}>
                            IP: {log.ip}
                          </RTLText>
                        )}
                      </div>
                    </div>
                  </div>
                  <Eye className="w-4 h-4 text-gray-400" />
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Activity size={48} className="mx-auto text-gray-400 mb-4" />
            <RTLText className="text-gray-400">
              {t('admin.logs.noLogsFound', 'No logs found')}
            </RTLText>
          </div>
        )}
      </div>

      {/* Log Detail Modal */}
      {selectedLog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="card p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <RTLText as="h3" className="text-lg font-semibold">
                {t('admin.logs.logDetails', 'Log Details')}
              </RTLText>
              <button
                onClick={() => setSelectedLog(null)}
                className="text-gray-400 hover:text-white"
              >
                <XCircle size={20} />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <RTLText as="label" className="text-sm font-medium text-gray-400">
                  {t('admin.logs.timestamp', 'Timestamp')}
                </RTLText>
                <RTLText className="block mt-1">
                  {formatTimestamp(selectedLog.timestamp)}
                </RTLText>
              </div>

              <div>
                <RTLText as="label" className="text-sm font-medium text-gray-400">
                  {t('admin.logs.level', 'Level')}
                </RTLText>
                <div className={`flex items-center mt-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  {getLevelIcon(selectedLog.level)}
                  <RTLText className={`${isRTL ? 'mr-2' : 'ml-2'} capitalize`}>
                    {selectedLog.level}
                  </RTLText>
                </div>
              </div>

              <div>
                <RTLText as="label" className="text-sm font-medium text-gray-400">
                  {t('admin.logs.category', 'Category')}
                </RTLText>
                <RTLText className="block mt-1">
                  {selectedLog.category}
                </RTLText>
              </div>

              <div>
                <RTLText as="label" className="text-sm font-medium text-gray-400">
                  {t('admin.logs.message', 'Message')}
                </RTLText>
                <RTLText className="block mt-1">
                  {selectedLog.message}
                </RTLText>
              </div>

              {selectedLog.details && (
                <div>
                  <RTLText as="label" className="text-sm font-medium text-gray-400">
                    {t('admin.logs.details', 'Details')}
                  </RTLText>
                  <RTLText className="block mt-1 text-sm">
                    {selectedLog.details}
                  </RTLText>
                </div>
              )}

              {selectedLog.user && (
                <div>
                  <RTLText as="label" className="text-sm font-medium text-gray-400">
                    {t('admin.logs.user', 'User')}
                  </RTLText>
                  <RTLText className="block mt-1">
                    {selectedLog.user}
                  </RTLText>
                </div>
              )}

              {selectedLog.ip && (
                <div>
                  <RTLText as="label" className="text-sm font-medium text-gray-400">
                    {t('admin.logs.ipAddress', 'IP Address')}
                  </RTLText>
                  <RTLText className="block mt-1">
                    {selectedLog.ip}
                  </RTLText>
                </div>
              )}

              {selectedLog.source && (
                <div>
                  <RTLText as="label" className="text-sm font-medium text-gray-400">
                    {t('admin.logs.source', 'Source')}
                  </RTLText>
                  <RTLText className="block mt-1">
                    {selectedLog.source}
                  </RTLText>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default SystemLogsPage;
