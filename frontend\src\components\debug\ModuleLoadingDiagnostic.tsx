import React, { useState, useEffect } from 'react';
import { AlertTriangle, CheckCircle, XCircle, RefreshCw } from 'lucide-react';

interface ModuleTest {
  name: string;
  path: string;
  status: 'loading' | 'success' | 'error';
  error?: string;
}

/**
 * Diagnostic component to test module loading
 * Only visible in development mode
 */
const ModuleLoadingDiagnostic: React.FC = () => {
  const [tests, setTests] = useState<ModuleTest[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const moduleTests = [
    { name: 'BusinessIdeasPage', path: '../pages/dashboard/BusinessIdeasPage' },
    { name: 'BusinessIncubator', path: '../components/incubator/BusinessIncubator' },
    { name: 'BusinessIdeaForm', path: '../components/incubator/forms/BusinessIdeaForm' },
    { name: 'incubator<PERSON><PERSON>', path: '../services/incubatorApi' },
    { name: 'useCRUD', path: '../hooks/useCRUD' },
  ];

  const runTests = async () => {
    setTests(moduleTests.map(test => ({ ...test, status: 'loading' as const })));

    for (const test of moduleTests) {
      try {
        await import(test.path);
        setTests(prev => prev.map(t => 
          t.name === test.name 
            ? { ...t, status: 'success' as const }
            : t
        ));
      } catch (error) {
        setTests(prev => prev.map(t => 
          t.name === test.name 
            ? { ...t, status: 'error' as const, error: error instanceof Error ? error.message : String(error) }
            : t
        ));
      }
    }
  };

  const getStatusIcon = (status: ModuleTest['status']) => {
    switch (status) {
      case 'loading':
        return <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
    }
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-20 right-4 z-50 bg-orange-600 text-white p-2 rounded-lg shadow-lg hover:bg-orange-700 transition-colors"
        title="Module Loading Diagnostic"
      >
        <AlertTriangle className="w-4 h-4" />
      </button>
    );
  }

  return (
    <div className="fixed bottom-20 right-4 z-50 bg-gray-900 border border-gray-700 rounded-lg shadow-lg max-w-md">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-700">
        <h3 className="text-white font-medium">Module Loading Tests</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white"
        >
          ×
        </button>
      </div>

      {/* Content */}
      <div className="p-3 space-y-3">
        <button
          onClick={runTests}
          className="w-full bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700 transition-colors"
        >
          Run Tests
        </button>

        {tests.length > 0 && (
          <div className="space-y-2">
            {tests.map(test => (
              <div key={test.name} className="flex items-center justify-between text-sm">
                <span className="text-gray-300">{test.name}</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(test.status)}
                  {test.error && (
                    <span className="text-red-400 text-xs max-w-32 truncate" title={test.error}>
                      {test.error}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="text-xs text-gray-400 border-t border-gray-700 pt-2">
          <p>This diagnostic helps identify module loading issues.</p>
          <p>If BusinessIdeasPage fails, check console for details.</p>
        </div>
      </div>
    </div>
  );
};

export default ModuleLoadingDiagnostic;
