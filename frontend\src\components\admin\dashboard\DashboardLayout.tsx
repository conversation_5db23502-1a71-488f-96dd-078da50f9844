/**
 * Dashboard Layout Component
 * Legacy layout component for backward compatibility
 * 
 * NOTE: This component is deprecated and should be replaced with AuthenticatedLayout
 * It's only kept for backward compatibility with existing admin dashboard components
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

interface DashboardLayoutProps {
  children: React.ReactNode;
  currentPage?: string;
  className?: string;
}

/**
 * @deprecated Use AuthenticatedLayout with UniversalSidebar instead
 * This component is only for backward compatibility
 */
const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  currentPage,
  className = '',
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // Log deprecation warning in development
  if (process.env.NODE_ENV === 'development') {
    console.warn(
      '⚠️ DashboardLayout is deprecated. Use AuthenticatedLayout with UniversalSidebar instead.',
      { currentPage }
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 ${isRTL ? 'rtl' : 'ltr'} ${className}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className={`px-4 sm:px-6 lg:px-8 py-6 ${isRTL ? 'text-right' : 'text-left'}`}>
        <div className="max-w-7xl mx-auto">
          {/* Deprecation Notice (Development Only) */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mb-6 p-4 bg-yellow-900/50 border border-yellow-500/50 rounded-lg">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-200">
                    {t('dashboard.layout.deprecated', 'Deprecated Component')}
                  </h3>
                  <div className="mt-2 text-sm text-yellow-100">
                    <p>
                      {t('dashboard.layout.deprecatedMessage', 'DashboardLayout is deprecated. Please use AuthenticatedLayout with UniversalSidebar instead.')}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Main Content */}
          {children}
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;
