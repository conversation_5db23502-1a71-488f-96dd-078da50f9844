import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Activity, Cpu, HardDrive, Monitor, Network, Zap,
  TrendingUp, TrendingDown, AlertTriangle, CheckCircle,
  Users, Database, Globe, Clock, RefreshCw, Pause, Play
} from 'lucide-react';
import DashboardLayout from '../dashboard/DashboardLayout';
import { superAdminApi } from '../../../services/superAdminApi';

interface SystemMetrics {
  timestamp: string;
  cpu: { usage: number; cores: number; temperature?: number };
  memory: { used: number; total: number; percent: number };
  disk: { used: number; total: number; percent: number };
  network: { upload: number; download: number; connections: number };
  processes: { total: number; active: number; sleeping: number };
  uptime: number;
  load_average: number[];
}

interface AlertData {
  id: string;
  type: 'warning' | 'error' | 'info';
  message: string;
  timestamp: string;
  resolved: boolean;
}

interface PerformanceData {
  response_time: number;
  throughput: number;
  error_rate: number;
  active_sessions: number;
}

const RealTimeSystemDashboard: React.FC = () => {
  const { t } = useTranslation();
  const [metrics, setMetrics] = useState<SystemMetrics[]>([]);
  const [currentMetrics, setCurrentMetrics] = useState<SystemMetrics | null>(null);
  const [alerts, setAlerts] = useState<AlertData[]>([]);
  const [performance, setPerformance] = useState<PerformanceData | null>(null);
  const [isLive, setIsLive] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(5000); // 5 seconds
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const maxDataPoints = 50; // Keep last 50 data points

  useEffect(() => {
    if (isLive) {
      startRealTimeUpdates();
    } else {
      stopRealTimeUpdates();
    }

    return () => stopRealTimeUpdates();
  }, [isLive, refreshInterval]);

  const startRealTimeUpdates = () => {
    fetchSystemMetrics(); // Initial fetch
    
    intervalRef.current = setInterval(() => {
      fetchSystemMetrics();
    }, refreshInterval);
  };

  const stopRealTimeUpdates = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  const fetchSystemMetrics = async () => {
    try {
      // Fetch real-time system metrics
      const metricsResult = await superAdminApi.getSystemHealth();
      if (metricsResult.success && metricsResult.data) {
        const newMetrics: SystemMetrics = {
          timestamp: new Date().toISOString(),
          cpu: {
            usage: metricsResult.data.cpu?.usage_percent || 0,
            cores: metricsResult.data.cpu?.count || 0,
            temperature: Math.random() * 20 + 40 // Mock temperature
          },
          memory: {
            used: metricsResult.data.memory?.used || 0,
            total: metricsResult.data.memory?.total || 0,
            percent: metricsResult.data.memory?.percent || 0
          },
          disk: {
            used: metricsResult.data.disk?.used || 0,
            total: metricsResult.data.disk?.total || 0,
            percent: metricsResult.data.disk?.percent || 0
          },
          network: {
            upload: Math.random() * 100,
            download: Math.random() * 500,
            connections: Math.floor(Math.random() * 1000) + 100
          },
          processes: {
            total: Math.floor(Math.random() * 200) + 150,
            active: Math.floor(Math.random() * 50) + 20,
            sleeping: Math.floor(Math.random() * 100) + 80
          },
          uptime: Date.now() - (Math.random() * 86400000 * 30), // Random uptime up to 30 days
          load_average: [
            Math.random() * 2,
            Math.random() * 2,
            Math.random() * 2
          ]
        };

        setCurrentMetrics(newMetrics);
        setMetrics(prev => {
          const updated = [...prev, newMetrics];
          return updated.slice(-maxDataPoints); // Keep only last N points
        });

        // Check for alerts
        checkForAlerts(newMetrics);
      }

      // Fetch performance metrics
      const perfResult = await superAdminApi.getPerformanceMetrics();
      if (perfResult.success && perfResult.data) {
        setPerformance(perfResult.data);
      }

    } catch (error) {
      console.error('Error fetching real-time metrics:', error);
    }
  };

  const checkForAlerts = (metrics: SystemMetrics) => {
    const newAlerts: AlertData[] = [];

    // CPU usage alert
    if (metrics.cpu.usage > 80) {
      newAlerts.push({
        id: `cpu-${Date.now()}`,
        type: 'warning',
        message: `High CPU usage: ${metrics.cpu.usage.toFixed(1)}%`,
        timestamp: new Date().toISOString(),
        resolved: false
      });
    }

    // Memory usage alert
    if (metrics.memory.percent > 85) {
      newAlerts.push({
        id: `memory-${Date.now()}`,
        type: 'error',
        message: `Critical memory usage: ${metrics.memory.percent.toFixed(1)}%`,
        timestamp: new Date().toISOString(),
        resolved: false
      });
    }

    // Disk usage alert
    if (metrics.disk.percent > 90) {
      newAlerts.push({
        id: `disk-${Date.now()}`,
        type: 'error',
        message: `Critical disk usage: ${metrics.disk.percent.toFixed(1)}%`,
        timestamp: new Date().toISOString(),
        resolved: false
      });
    }

    if (newAlerts.length > 0) {
      setAlerts(prev => [...newAlerts, ...prev].slice(0, 20)); // Keep last 20 alerts
    }
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatUptime = (uptime: number) => {
    const days = Math.floor(uptime / (1000 * 60 * 60 * 24));
    const hours = Math.floor((uptime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
    return `${days}d ${hours}h ${minutes}m`;
  };

  const getStatusColor = (value: number, thresholds: { warning: number; critical: number }) => {
    if (value >= thresholds.critical) return 'text-red-400';
    if (value >= thresholds.warning) return 'text-yellow-400';
    return 'text-green-400';
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'error': return <AlertTriangle className="w-4 h-4 text-red-400" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-400" />;
      default: return <CheckCircle className="w-4 h-4 text-blue-400" />;
    }
  };

  return (
    <DashboardLayout currentPage="super-admin">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">
              {t('superAdmin.realTimeDashboard.title', 'Real-Time System Dashboard')}
            </h1>
            <p className="text-gray-300 mt-2">
              {t('superAdmin.realTimeDashboard.subtitle', 'Live system monitoring and performance metrics')}
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            <select
              value={refreshInterval}
              onChange={(e) => setRefreshInterval(Number(e.target.value))}
              className="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white"
            >
              <option value={1000}>1 second</option>
              <option value={5000}>5 seconds</option>
              <option value={10000}>10 seconds</option>
              <option value={30000}>30 seconds</option>
            </select>
            
            <button
              onClick={() => setIsLive(!isLive)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                isLive 
                  ? 'bg-red-600 hover:bg-red-700 text-white' 
                  : 'bg-green-600 hover:bg-green-700 text-white'
              }`}
            >
              {isLive ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              {isLive ? 'Pause' : 'Resume'}
            </button>
          </div>
        </div>

        {/* Live Status Indicator */}
        <div className="flex items-center gap-2 text-sm">
          <div className={`w-2 h-2 rounded-full ${isLive ? 'bg-green-400 animate-pulse' : 'bg-gray-400'}`}></div>
          <span className="text-gray-300">
            {isLive ? 'Live Updates Active' : 'Updates Paused'} • 
            Last updated: {currentMetrics ? new Date(currentMetrics.timestamp).toLocaleTimeString() : 'Never'}
          </span>
        </div>

        {/* Current Metrics Grid */}
        {currentMetrics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* CPU Usage */}
            <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <Cpu className="w-5 h-5 text-blue-400" />
                  <span className="font-medium text-white">CPU Usage</span>
                </div>
                <span className={`text-2xl font-bold ${getStatusColor(currentMetrics.cpu.usage, { warning: 70, critical: 85 })}`}>
                  {currentMetrics.cpu.usage.toFixed(1)}%
                </span>
              </div>
              <div className="space-y-2">
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      currentMetrics.cpu.usage > 85 ? 'bg-red-500' :
                      currentMetrics.cpu.usage > 70 ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${currentMetrics.cpu.usage}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-sm text-gray-400">
                  <span>{currentMetrics.cpu.cores} cores</span>
                  <span>{currentMetrics.cpu.temperature?.toFixed(1)}°C</span>
                </div>
              </div>
            </div>

            {/* Memory Usage */}
            <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <Monitor className="w-5 h-5 text-green-400" />
                  <span className="font-medium text-white">Memory</span>
                </div>
                <span className={`text-2xl font-bold ${getStatusColor(currentMetrics.memory.percent, { warning: 75, critical: 90 })}`}>
                  {currentMetrics.memory.percent.toFixed(1)}%
                </span>
              </div>
              <div className="space-y-2">
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      currentMetrics.memory.percent > 90 ? 'bg-red-500' :
                      currentMetrics.memory.percent > 75 ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${currentMetrics.memory.percent}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-sm text-gray-400">
                  <span>{formatBytes(currentMetrics.memory.used)}</span>
                  <span>{formatBytes(currentMetrics.memory.total)}</span>
                </div>
              </div>
            </div>

            {/* Disk Usage */}
            <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <HardDrive className="w-5 h-5 text-purple-400" />
                  <span className="font-medium text-white">Disk Space</span>
                </div>
                <span className={`text-2xl font-bold ${getStatusColor(currentMetrics.disk.percent, { warning: 80, critical: 95 })}`}>
                  {currentMetrics.disk.percent.toFixed(1)}%
                </span>
              </div>
              <div className="space-y-2">
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      currentMetrics.disk.percent > 95 ? 'bg-red-500' :
                      currentMetrics.disk.percent > 80 ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${currentMetrics.disk.percent}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-sm text-gray-400">
                  <span>{formatBytes(currentMetrics.disk.used)}</span>
                  <span>{formatBytes(currentMetrics.disk.total)}</span>
                </div>
              </div>
            </div>

            {/* Network Activity */}
            <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <Network className="w-5 h-5 text-cyan-400" />
                  <span className="font-medium text-white">Network</span>
                </div>
                <span className="text-2xl font-bold text-cyan-400">
                  {currentMetrics.network.connections}
                </span>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Upload:</span>
                  <span className="text-green-400">{currentMetrics.network.upload.toFixed(1)} MB/s</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Download:</span>
                  <span className="text-blue-400">{currentMetrics.network.download.toFixed(1)} MB/s</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Connections:</span>
                  <span className="text-cyan-400">{currentMetrics.network.connections}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* System Information */}
        {currentMetrics && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <Clock className="w-5 h-5 text-yellow-400" />
                System Uptime
              </h3>
              <div className="text-2xl font-bold text-yellow-400 mb-2">
                {formatUptime(currentMetrics.uptime)}
              </div>
              <div className="text-sm text-gray-400">
                Since: {new Date(Date.now() - currentMetrics.uptime).toLocaleString()}
              </div>
            </div>

            <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <Activity className="w-5 h-5 text-orange-400" />
                Load Average
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-400">1 min:</span>
                  <span className="text-orange-400">{currentMetrics.load_average[0]?.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">5 min:</span>
                  <span className="text-orange-400">{currentMetrics.load_average[1]?.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">15 min:</span>
                  <span className="text-orange-400">{currentMetrics.load_average[2]?.toFixed(2)}</span>
                </div>
              </div>
            </div>

            <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <Zap className="w-5 h-5 text-green-400" />
                Processes
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-400">Total:</span>
                  <span className="text-green-400">{currentMetrics.processes.total}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Active:</span>
                  <span className="text-green-400">{currentMetrics.processes.active}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Sleeping:</span>
                  <span className="text-green-400">{currentMetrics.processes.sleeping}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Alerts Section */}
        {alerts.length > 0 && (
          <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-red-400" />
              System Alerts ({alerts.filter(a => !a.resolved).length} active)
            </h3>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {alerts.slice(0, 10).map((alert) => (
                <div
                  key={alert.id}
                  className={`flex items-center gap-3 p-3 rounded-lg border ${
                    alert.resolved 
                      ? 'bg-gray-700/50 border-gray-600' 
                      : alert.type === 'error' 
                        ? 'bg-red-900/20 border-red-500/20' 
                        : 'bg-yellow-900/20 border-yellow-500/20'
                  }`}
                >
                  {getAlertIcon(alert.type)}
                  <div className="flex-1">
                    <p className={`text-sm ${alert.resolved ? 'text-gray-400' : 'text-white'}`}>
                      {alert.message}
                    </p>
                    <p className="text-xs text-gray-500">
                      {new Date(alert.timestamp).toLocaleTimeString()}
                    </p>
                  </div>
                  {!alert.resolved && (
                    <button
                      onClick={() => setAlerts(prev => 
                        prev.map(a => a.id === alert.id ? { ...a, resolved: true } : a)
                      )}
                      className="text-xs bg-gray-600 hover:bg-gray-700 px-2 py-1 rounded transition-colors"
                    >
                      Resolve
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default RealTimeSystemDashboard;
