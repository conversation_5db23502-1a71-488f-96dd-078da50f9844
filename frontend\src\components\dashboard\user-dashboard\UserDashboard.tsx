import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../../store/hooks';
import { useLanguage } from '../../../hooks/useLanguage';
import {
  Lightbulb,
  FileText,
  BarChart3,
  Plus,
  MessageSquare,
  BookOpen,
  Star,
  Clock
} from 'lucide-react';

/**
 * SIMPLE USER DASHBOARD
 *
 * Clean, user-focused dashboard with:
 * - Personal welcome
 * - Simple stats
 * - Easy actions
 * - Recent activity
 * - No complex business/incubator features
 */
const UserDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector((state) => state.auth);

  // Simple user stats (no business/incubator complexity)
  const userStats = [
    {
      title: t('dashboard.stats.myIdeas', 'My Ideas'),
      value: '0',
      icon: Lightbulb,
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/20',
      description: t('dashboard.stats.ideasDesc', 'Ideas created')
    },
    {
      title: t('dashboard.stats.templates', 'Templates Used'),
      value: '0',
      icon: FileText,
      color: 'text-green-400',
      bgColor: 'bg-green-500/20',
      description: t('dashboard.stats.templatesDesc', 'Templates explored')
    },
    {
      title: t('dashboard.stats.aiChats', 'AI Conversations'),
      value: '0',
      icon: MessageSquare,
      color: 'text-purple-400',
      bgColor: 'bg-purple-500/20',
      description: t('dashboard.stats.chatsDesc', 'AI conversations')
    }
  ];

  // Simple user actions
  const userActions = [
    {
      title: t('dashboard.actions.newIdea', 'New Idea'),
      description: t('dashboard.actions.newIdeaDesc', 'Create a new business idea'),
      icon: Plus,
      href: '/dashboard/business-ideas/new',
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      title: t('dashboard.actions.templates', 'Templates'),
      description: t('dashboard.actions.templatesDesc', 'Browse business templates'),
      icon: BookOpen,
      href: '/dashboard/templates',
      color: 'bg-green-500 hover:bg-green-600'
    },
    {
      title: t('dashboard.actions.aiHelp', 'AI Help'),
      description: t('dashboard.actions.aiHelpDesc', 'Get AI assistance'),
      icon: MessageSquare,
      href: '/chat/enhanced',
      color: 'bg-purple-500 hover:bg-purple-600'
    }
  ];

  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className={`p-6 md:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="max-w-6xl mx-auto">

          {/* Welcome Section */}
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-gray-800 mb-3">
              {t('dashboard.welcome.greeting', 'مرحباً, {{name}}!', { name: user?.username || 'User' })}
            </h1>
            <p className="text-gray-600 text-lg">
              {t('dashboard.welcome.subtitle', 'مرحباً بك في لوحة التحكم الخاصة بك')}
            </p>
          </div>

          {/* User Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {userStats.map((stat, index) => (
              <div
                key={index}
                className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-3 rounded-xl ${stat.bgColor}`}>
                    <stat.icon className={`w-6 h-6 ${stat.color}`} />
                  </div>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-800 mb-1">{stat.value}</h3>
                  <p className="text-gray-600 text-sm font-medium">{stat.title}</p>
                  <p className="text-gray-500 text-xs mt-1">{stat.description}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Getting Started Card */}
          <div className="mb-8">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-8 text-white text-center shadow-xl">
              <div className="text-5xl mb-4">💡</div>
              <h3 className="text-2xl font-bold mb-4">
                {t('dashboard.welcome.title', 'Ready to get started?')}
              </h3>
              <p className="text-blue-100 mb-6 max-w-xl mx-auto">
                {t('dashboard.welcome.description', 'Create your first business idea or explore our templates to begin your journey.')}
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="/dashboard/business-ideas/new"
                  className="px-6 py-3 bg-white text-blue-600 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
                >
                  {t('dashboard.welcome.createIdea', 'Create New Idea')}
                </a>
                <a
                  href="/dashboard/templates"
                  className="px-6 py-3 bg-white/20 text-white rounded-lg font-semibold hover:bg-white/30 transition-colors"
                >
                  {t('dashboard.welcome.browseTemplates', 'Browse Templates')}
                </a>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mb-8">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-blue-500 rounded-lg">
                <Plus className="w-6 h-6 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-800">
                {t('dashboard.quickActions.title', 'Quick Actions')}
              </h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {userActions.map((action, index) => (
                <a
                  key={index}
                  href={action.href}
                  className={`${action.color} text-white rounded-xl p-6 transition-all duration-300 block group hover:scale-105 hover:shadow-2xl transform`}
                >
                  <div className="flex items-center mb-4">
                    <div className="p-3 bg-white/20 rounded-lg mr-4 group-hover:bg-white/30 transition-colors duration-300">
                      <action.icon className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
                    </div>
                    <h3 className="text-lg font-bold group-hover:text-white/90">{action.title}</h3>
                  </div>
                  <p className="text-white/80 text-sm leading-relaxed group-hover:text-white/90">{action.description}</p>
                  <div className="mt-4 flex items-center text-white/60 group-hover:text-white/80 transition-colors duration-300">
                    <span className="text-xs font-medium">Click to start</span>
                    <MessageSquare className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                  </div>
                </a>
              ))}
            </div>
          </div>

          {/* Recent Activity */}
          <div className="mb-8">
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                <Clock className="w-5 h-5 text-blue-500" />
                {t('dashboard.recentActivity', 'Recent Activity')}
              </h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-gray-700 text-sm">
                      {t('dashboard.activity.welcome', 'Welcome to your dashboard!')}
                    </p>
                    <p className="text-gray-500 text-xs">Just now</p>
                  </div>
                </div>
                <div className="text-center py-8">
                  <div className="text-4xl mb-2">📝</div>
                  <p className="text-gray-500 text-sm">
                    {t('dashboard.noActivity', 'No recent activity. Start by creating your first business idea!')}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Simple Help Section */}
          <div className="mb-8">
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                <Star className="w-5 h-5 text-yellow-500" />
                {t('dashboard.help.title', 'Getting Started')}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4">
                  <div className="text-3xl mb-2">💡</div>
                  <h4 className="font-semibold text-gray-800 mb-1">
                    {t('dashboard.help.step1', 'Create Ideas')}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {t('dashboard.help.step1Desc', 'Start with your first business idea')}
                  </p>
                </div>
                <div className="text-center p-4">
                  <div className="text-3xl mb-2">📚</div>
                  <h4 className="font-semibold text-gray-800 mb-1">
                    {t('dashboard.help.step2', 'Use Templates')}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {t('dashboard.help.step2Desc', 'Explore our business templates')}
                  </p>
                </div>
                <div className="text-center p-4">
                  <div className="text-3xl mb-2">🤖</div>
                  <h4 className="font-semibold text-gray-800 mb-1">
                    {t('dashboard.help.step3', 'Get AI Help')}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {t('dashboard.help.step3Desc', 'Chat with our AI assistant')}
                  </p>
                </div>
              </div>
            </div>
          </div>



        </div>
      </div>
    </div>
  );
};

export default UserDashboard;
