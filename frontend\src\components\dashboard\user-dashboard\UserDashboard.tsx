import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../../store/hooks';
import { useLanguage } from '../../../hooks/useLanguage';
import {
  Lightbulb,
  FileText,
  BarChart3,
  Users,
  TrendingUp,
  Plus,
  Eye,
  Edit,
  MessageSquare,
  Zap,
  Target,
  Calendar
} from 'lucide-react';

/**
 * CLEAN USER DASHBOARD COMPONENT
 *
 * Simplified, working dashboard for regular users with:
 * - Welcome section
 * - Quick stats
 * - Quick actions
 * - No complex dependencies or problematic components
 */
const UserDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector((state) => state.auth);

  // Quick stats data (simplified, no complex API calls)
  const quickStats = [
    {
      title: t('dashboard.stats.totalIdeas', 'Total Ideas'),
      value: '0',
      icon: Lightbulb,
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-500/20',
      description: t('dashboard.stats.getStarted', 'Get started!')
    },
    {
      title: t('dashboard.stats.approved', 'Approved'),
      value: '0',
      icon: Target,
      color: 'text-green-400',
      bgColor: 'bg-green-500/20'
    },
    {
      title: t('dashboard.stats.pending', 'Pending'),
      value: '0',
      icon: Calendar,
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/20'
    },
    {
      title: t('dashboard.stats.rejected', 'Rejected'),
      value: '0',
      icon: TrendingUp,
      color: 'text-red-400',
      bgColor: 'bg-red-500/20'
    }
  ];

  // Quick actions data
  const quickActions = [
    {
      title: t('dashboard.actions.createIdea', 'Create Business Idea'),
      description: t('dashboard.actions.createIdeaDesc', 'Start your entrepreneurial journey'),
      icon: Plus,
      href: '/dashboard/business-ideas/new',
      color: 'bg-purple-600 hover:bg-purple-700'
    },
    {
      title: t('dashboard.actions.browseTemplates', 'Browse Templates'),
      description: t('dashboard.actions.browseTemplatesDesc', 'Use proven business templates'),
      icon: FileText,
      href: '/dashboard/templates',
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      title: t('dashboard.actions.viewAnalytics', 'View Analytics'),
      description: t('dashboard.actions.viewAnalyticsDesc', 'Track your progress'),
      icon: BarChart3,
      href: '/dashboard/analytics',
      color: 'bg-green-600 hover:bg-green-700'
    },
    {
      title: t('dashboard.actions.aiChat', 'AI Assistant'),
      description: t('dashboard.actions.aiChatDesc', 'Get AI-powered guidance'),
      icon: Zap,
      href: '/chat/enhanced',
      color: 'bg-indigo-600 hover:bg-indigo-700'
    }
  ];

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="max-w-7xl mx-auto w-full">

          {/* Welcome Section */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">
              {t('dashboard.welcome.greeting', 'مرحباً, {{name}}!', { name: user?.username || 'User' })}
            </h1>
            <p className="text-purple-200">
              {t('dashboard.welcome.subtitle', 'نظرة عامة')}
            </p>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {quickStats.map((stat, index) => (
              <div
                key={index}
                className="bg-gradient-to-br from-white/15 to-white/5 backdrop-blur-md border border-white/30 rounded-xl p-6 hover:from-white/20 hover:to-white/10 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-3 rounded-xl ${stat.bgColor} shadow-lg`}>
                    <stat.icon className={`w-7 h-7 ${stat.color}`} />
                  </div>
                  <div className="text-right">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  </div>
                </div>
                <div>
                  <p className="text-gray-300 text-sm mb-2 font-medium">{stat.title}</p>
                  <h3 className="text-3xl font-bold text-white mb-1">{stat.value}</h3>
                  {stat.description && (
                    <p className="text-purple-300 text-sm font-medium">{stat.description}</p>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Welcome Card for New Users */}
          <div className="mb-8">
            <div className="relative bg-gradient-to-r from-purple-900/60 via-indigo-900/60 to-blue-900/60 backdrop-blur-md border border-purple-500/40 rounded-2xl p-8 text-center overflow-hidden shadow-2xl">
              {/* Background decoration */}
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 to-blue-600/10"></div>
              <div className="absolute top-0 right-0 w-32 h-32 bg-purple-500/20 rounded-full -translate-y-16 translate-x-16"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-blue-500/20 rounded-full translate-y-12 -translate-x-12"></div>

              <div className="relative z-10">
                <div className="text-7xl mb-6 animate-bounce">🚀</div>
                <h3 className="text-3xl font-bold text-white mb-4 bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent">
                  {t('dashboard.welcome.title', 'Welcome to Your Dashboard!')}
                </h3>
                <p className="text-purple-100 mb-8 max-w-2xl mx-auto text-lg leading-relaxed">
                  {t('dashboard.welcome.description', 'Start your entrepreneurial journey by creating your first business idea. Our AI will help you develop and refine it.')}
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href="/dashboard/business-ideas/new"
                    className="group px-8 py-4 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                  >
                    <span className="flex items-center justify-center gap-2">
                      <Plus className="w-5 h-5 group-hover:rotate-90 transition-transform duration-300" />
                      {t('dashboard.welcome.createIdea', 'Create Your First Business Idea')}
                    </span>
                  </a>
                  <a
                    href="/dashboard/templates"
                    className="group px-8 py-4 bg-transparent border-2 border-purple-400 text-purple-200 hover:bg-purple-500/20 hover:border-purple-300 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                  >
                    <span className="flex items-center justify-center gap-2">
                      <FileText className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
                      {t('dashboard.welcome.browseTemplates', 'Browse Templates')}
                    </span>
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mb-8">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-white">
                {t('dashboard.quickActions.title', 'Quick Actions')}
              </h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {quickActions.map((action, index) => (
                <a
                  key={index}
                  href={action.href}
                  className={`${action.color} text-white rounded-xl p-6 transition-all duration-300 block group hover:scale-105 hover:shadow-2xl transform`}
                >
                  <div className="flex items-center mb-4">
                    <div className="p-3 bg-white/20 rounded-lg mr-4 group-hover:bg-white/30 transition-colors duration-300">
                      <action.icon className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
                    </div>
                    <h3 className="text-lg font-bold group-hover:text-white/90">{action.title}</h3>
                  </div>
                  <p className="text-white/80 text-sm leading-relaxed group-hover:text-white/90">{action.description}</p>
                  <div className="mt-4 flex items-center text-white/60 group-hover:text-white/80 transition-colors duration-300">
                    <span className="text-xs font-medium">Click to start</span>
                    <MessageSquare className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                  </div>
                </a>
              ))}
            </div>
          </div>

          {/* AI Assistant Status */}
          <div className="mb-8">
            <div className="bg-gradient-to-r from-indigo-900/60 to-purple-900/60 backdrop-blur-md border border-indigo-500/40 rounded-xl p-6 shadow-xl">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="p-3 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg">
                    <Zap className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white">
                      {t('dashboard.ai.title', 'مساعد ياسمين للذكاء الاصطناعي')} 🤖
                    </h3>
                    <p className="text-indigo-200 text-sm">
                      {t('dashboard.ai.subtitle', 'أدوات المحادثة والتحليل المتقدمة للذكاء الاصطناعي')}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-300 text-sm font-medium">
                    {t('dashboard.ai.status', 'متاح')}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="bg-white/10 rounded-lg p-4 text-center">
                  <MessageSquare className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                  <p className="text-white font-semibold">20/hr</p>
                  <p className="text-gray-300 text-sm">{t('dashboard.ai.chatLimit', 'رسائل المحادثة')}</p>
                </div>
                <div className="bg-white/10 rounded-lg p-4 text-center">
                  <BarChart3 className="w-8 h-8 text-green-400 mx-auto mb-2" />
                  <p className="text-white font-semibold">5/day</p>
                  <p className="text-gray-300 text-sm">{t('dashboard.ai.analysisLimit', 'التحليل')}</p>
                </div>
                <div className="bg-white/10 rounded-lg p-4 text-center">
                  <Target className="w-8 h-8 text-purple-400 mx-auto mb-2" />
                  <p className="text-white font-semibold">3/day</p>
                  <p className="text-gray-300 text-sm">{t('dashboard.ai.generationLimit', 'التوليد')}</p>
                </div>
              </div>

              <a
                href="/chat/enhanced"
                className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-lg py-3 px-6 font-semibold transition-all duration-300 flex items-center justify-center gap-2 group"
              >
                <MessageSquare className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
                {t('dashboard.ai.startChat', 'بدء محادثة مع الذكاء الاصطناعي')}
              </a>
            </div>
          </div>

          {/* Progress & Activity Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* Recent Activity */}
            <div className="bg-gradient-to-br from-white/15 to-white/5 backdrop-blur-md border border-white/30 rounded-xl p-6 shadow-lg">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg">
                  <Calendar className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-lg font-bold text-white">
                  {t('dashboard.recentActivity', 'Recent Activity')}
                </h3>
              </div>
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-3 bg-white/10 rounded-lg">
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  <p className="text-gray-300 text-sm">
                    {t('dashboard.activity.welcome', 'Welcome to Yasmeen AI Dashboard!')}
                  </p>
                  <span className="text-gray-400 text-xs ml-auto">now</span>
                </div>
                <div className="text-center py-4">
                  <Users className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-400 text-sm">
                    {t('dashboard.noActivity', 'No recent activity. Start by creating your first business idea!')}
                  </p>
                </div>
              </div>
            </div>

            {/* Progress Tracking */}
            <div className="bg-gradient-to-br from-white/15 to-white/5 backdrop-blur-md border border-white/30 rounded-xl p-6 shadow-lg">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
                  <TrendingUp className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-lg font-bold text-white">
                  {t('dashboard.progress.title', 'Your Progress')}
                </h3>
              </div>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-300 text-sm">{t('dashboard.progress.profile', 'Profile Completion')}</span>
                    <span className="text-white font-semibold">25%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full w-1/4"></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-300 text-sm">{t('dashboard.progress.ideas', 'Business Ideas')}</span>
                    <span className="text-white font-semibold">0%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div className="bg-gradient-to-r from-green-500 to-teal-500 h-2 rounded-full w-0"></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-300 text-sm">{t('dashboard.progress.engagement', 'Engagement')}</span>
                    <span className="text-white font-semibold">10%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div className="bg-gradient-to-r from-yellow-500 to-orange-500 h-2 rounded-full w-1/12"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tips & Resources */}
          <div className="mb-8">
            <div className="bg-gradient-to-r from-amber-900/60 to-orange-900/60 backdrop-blur-md border border-amber-500/40 rounded-xl p-6 shadow-xl">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-gradient-to-r from-amber-500 to-orange-500 rounded-lg">
                  <Lightbulb className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white">
                  {t('dashboard.tips.title', 'نصائح للبدء')}
                </h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-white/10 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-xs font-bold">1</div>
                    <h4 className="text-white font-semibold text-sm">{t('dashboard.tips.step1', 'أكمل ملفك الشخصي')}</h4>
                  </div>
                  <p className="text-gray-300 text-xs">
                    {t('dashboard.tips.step1Desc', 'أضف صورة شخصية ونبذة عنك لتحسين تجربتك')}
                  </p>
                </div>
                <div className="bg-white/10 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold">2</div>
                    <h4 className="text-white font-semibold text-sm">{t('dashboard.tips.step2', 'أنشئ فكرتك الأولى')}</h4>
                  </div>
                  <p className="text-gray-300 text-xs">
                    {t('dashboard.tips.step2Desc', 'ابدأ رحلتك الريادية بإنشاء أول فكرة تجارية')}
                  </p>
                </div>
                <div className="bg-white/10 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white text-xs font-bold">3</div>
                    <h4 className="text-white font-semibold text-sm">{t('dashboard.tips.step3', 'استخدم الذكاء الاصطناعي')}</h4>
                  </div>
                  <p className="text-gray-300 text-xs">
                    {t('dashboard.tips.step3Desc', 'احصل على إرشادات ونصائح من مساعد الذكاء الاصطناعي')}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a
              href="/dashboard/business-ideas"
              className="bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20 rounded-lg p-4 text-center transition-all duration-300 group"
            >
              <Lightbulb className="w-8 h-8 text-yellow-400 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
              <p className="text-white text-sm font-medium">{t('dashboard.links.ideas', 'الأفكار التجارية')}</p>
            </a>
            <a
              href="/dashboard/templates"
              className="bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20 rounded-lg p-4 text-center transition-all duration-300 group"
            >
              <FileText className="w-8 h-8 text-blue-400 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
              <p className="text-white text-sm font-medium">{t('dashboard.links.templates', 'القوالب')}</p>
            </a>
            <a
              href="/dashboard/analytics"
              className="bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20 rounded-lg p-4 text-center transition-all duration-300 group"
            >
              <BarChart3 className="w-8 h-8 text-green-400 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
              <p className="text-white text-sm font-medium">{t('dashboard.links.analytics', 'التحليلات')}</p>
            </a>
            <a
              href="/settings"
              className="bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20 rounded-lg p-4 text-center transition-all duration-300 group"
            >
              <Users className="w-8 h-8 text-purple-400 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
              <p className="text-white text-sm font-medium">{t('dashboard.links.settings', 'الإعدادات')}</p>
            </a>
          </div>

        </div>
      </div>
    </div>
  );
};

export default UserDashboard;
