import React, { useEffect, useState } from 'react';
import { Alert<PERSON>riangle, X } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../../store/hooks';
import { useLanguage } from '../../../hooks/useLanguage';
import { ErrorDisplay, useSessionError } from '../../ui';
import { UserActivityAnalytics } from '../user-activity';
import { ContentRecommendations } from '../content-recommendations';
import { ProfileCompletionCard } from '../profile-completion';
import { AIDashboardWidget } from '../AIDashboardWidget';
import EnhancedPredictiveAnalyticsDashboard from '../../analytics/EnhancedPredictiveAnalyticsDashboard';
import ContentAnalytics from '../ContentAnalytics';
import { useBusinessIdeas } from './hooks';
import {
  WelcomeSection,
  StatsCards,
  QuickActions,
  LatestIdeaCard,
  BusinessIdeaProgress
} from './components';

const UserDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector((state) => state.auth);
  const { businessIdeas, loading, error, stats, latestIdea } = useBusinessIdeas();
  const { error: accessError, clearError: clearAccessError } = useSessionError('accessError');

  // Show error state if there's an error loading business ideas
  if (error) {
    return (
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="bg-red-900/30 border border-red-500/30 rounded-lg p-6 text-center">
              <AlertTriangle className="w-12 h-12 text-red-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-red-300 mb-2">
                {t('dashboard.error.title', 'Dashboard Error')}
              </h2>
              <p className="text-red-200 mb-4">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                {t('common.retry', 'Retry')}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            {/* Access Error Alert */}
            <ErrorDisplay
              error={accessError}
              type="warning"
              onDismiss={clearAccessError}
              className="mb-6"
            />

        {/* Welcome Section */}
        <WelcomeSection user={user} />

        {loading ? (
          <div className={`flex flex-col items-center justify-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mb-4"></div>
            <p className="text-gray-300 text-lg">{t('dashboard.loading', 'Loading your dashboard...')}</p>
            <p className="text-gray-400 text-sm mt-2">{t('dashboard.loadingDescription', 'Fetching your business ideas and analytics')}</p>
          </div>
        ) : (
          <>
            {/* Stats Cards */}
            <StatsCards stats={stats} />

            {/* New User Welcome Message */}
            {stats.totalIdeas === 0 && (
              <div className="bg-gradient-to-r from-purple-900/40 to-blue-900/40 backdrop-blur-sm rounded-lg p-6 border border-purple-500/30 mb-8">
                <div className="text-center">
                  <div className="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-3xl">🚀</span>
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-2">
                    {t('dashboard.welcome.title', 'Welcome to Your Dashboard!')}
                  </h3>
                  <p className="text-gray-300 mb-4 max-w-2xl mx-auto">
                    {t('dashboard.welcome.description', 'Start your entrepreneurial journey by creating your first business idea. Our AI will help you develop and refine it.')}
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <a
                      href="/dashboard/business-ideas/new"
                      className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors"
                    >
                      {t('dashboard.welcome.createIdea', 'Create Your First Business Idea')}
                    </a>
                    <a
                      href="/dashboard/templates"
                      className="px-6 py-3 bg-transparent border border-purple-500 text-purple-300 hover:bg-purple-500/20 rounded-lg font-medium transition-colors"
                    >
                      {t('dashboard.welcome.browseTemplates', 'Browse Templates')}
                    </a>
                  </div>
                </div>
              </div>
            )}

            {/* Quick Actions */}
            <QuickActions />

            {/* AI Dashboard Widget */}
            <div className="mb-8">
              <AIDashboardWidget />
            </div>

            {/* Latest Business Idea */}
            {latestIdea && <LatestIdeaCard latestIdea={latestIdea} />}

            {/* Content Analytics */}
            <div className="mb-8">
              <ContentAnalytics />
            </div>

            {/* Activity Analytics and Profile Completion */}
            <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 mb-8">
              <div className="xl:col-span-2">
                <UserActivityAnalytics />
              </div>
              <div>
                <ProfileCompletionCard />
              </div>
            </div>

            {/* Enhanced Predictive Analytics */}
            {latestIdea && (
              <div className="mb-8">
                <EnhancedPredictiveAnalyticsDashboard
                  businessData={{
                    title: latestIdea.title,
                    description: latestIdea.description,
                    stage: latestIdea.stage || 1,
                    has_team: latestIdea.team_size > 1,
                    has_funding: latestIdea.funding_amount > 0,
                    monthly_revenue: latestIdea.monthly_revenue || 0,
                    burn_rate: latestIdea.burn_rate || 0,
                    runway_months: latestIdea.runway_months || 12,
                    customer_count: latestIdea.customer_count || 0,
                    growth_rate: latestIdea.growth_rate || 0,
                    team_size: latestIdea.team_size || 1,
                    founder_experience: latestIdea.founder_experience || 1,
                    market_size: latestIdea.market_size || 1000000,
                    competition_level: latestIdea.competition_level || 5,
                    industry_category: latestIdea.industry_category || 1,
                    business_model: latestIdea.business_model || 1,
                    target_market_size: latestIdea.target_market_size || 100000,
                    product_complexity: latestIdea.product_complexity || 0.5,
                    monthly_revenue_per_customer: latestIdea.monthly_revenue_per_customer || 50,
                    monthly_churn_rate: latestIdea.monthly_churn_rate || 0.05,
                    gross_margin: latestIdea.gross_margin || 0.7
                  }}
                />
              </div>
            )}

            {/* Content Recommendations */}
            <div className="mb-8">
              <ContentRecommendations />
            </div>

            {/* Business Idea Progress Tracking */}
            {latestIdea && <BusinessIdeaProgress latestIdea={latestIdea} />}
          </>
        )}
          </div>
        </div>
      </div>
  );
};

export default UserDashboard;
