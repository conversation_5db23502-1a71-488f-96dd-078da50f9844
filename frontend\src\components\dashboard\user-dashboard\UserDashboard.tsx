import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../../store/hooks';
import { useLanguage } from '../../../hooks/useLanguage';
import {
  Lightbulb,
  FileText,
  BarChart3,
  Users,
  TrendingUp,
  Plus,
  Eye,
  Edit,
  MessageSquare,
  Zap,
  Target,
  Calendar
} from 'lucide-react';

/**
 * CLEAN USER DASHBOARD COMPONENT
 *
 * Simplified, working dashboard for regular users with:
 * - Welcome section
 * - Quick stats
 * - Quick actions
 * - No complex dependencies or problematic components
 */
const UserDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector((state) => state.auth);

  // Quick stats data (simplified, no complex API calls)
  const quickStats = [
    {
      title: t('dashboard.stats.totalIdeas', 'Total Ideas'),
      value: '0',
      icon: Lightbulb,
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-500/20',
      description: t('dashboard.stats.getStarted', 'Get started!')
    },
    {
      title: t('dashboard.stats.approved', 'Approved'),
      value: '0',
      icon: Target,
      color: 'text-green-400',
      bgColor: 'bg-green-500/20'
    },
    {
      title: t('dashboard.stats.pending', 'Pending'),
      value: '0',
      icon: Calendar,
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/20'
    },
    {
      title: t('dashboard.stats.rejected', 'Rejected'),
      value: '0',
      icon: TrendingUp,
      color: 'text-red-400',
      bgColor: 'bg-red-500/20'
    }
  ];

  // Quick actions data
  const quickActions = [
    {
      title: t('dashboard.actions.createIdea', 'Create Business Idea'),
      description: t('dashboard.actions.createIdeaDesc', 'Start your entrepreneurial journey'),
      icon: Plus,
      href: '/dashboard/business-ideas/new',
      color: 'bg-purple-600 hover:bg-purple-700'
    },
    {
      title: t('dashboard.actions.browseTemplates', 'Browse Templates'),
      description: t('dashboard.actions.browseTemplatesDesc', 'Use proven business templates'),
      icon: FileText,
      href: '/dashboard/templates',
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      title: t('dashboard.actions.viewAnalytics', 'View Analytics'),
      description: t('dashboard.actions.viewAnalyticsDesc', 'Track your progress'),
      icon: BarChart3,
      href: '/dashboard/analytics',
      color: 'bg-green-600 hover:bg-green-700'
    },
    {
      title: t('dashboard.actions.aiChat', 'AI Assistant'),
      description: t('dashboard.actions.aiChatDesc', 'Get AI-powered guidance'),
      icon: Zap,
      href: '/chat/enhanced',
      color: 'bg-indigo-600 hover:bg-indigo-700'
    }
  ];

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="max-w-7xl mx-auto w-full">

          {/* Welcome Section */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">
              {t('dashboard.welcome.greeting', 'مرحباً, {{name}}!', { name: user?.username || 'User' })}
            </h1>
            <p className="text-purple-200">
              {t('dashboard.welcome.subtitle', 'نظرة عامة')}
            </p>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {quickStats.map((stat, index) => (
              <div
                key={index}
                className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                    <stat.icon className={`w-6 h-6 ${stat.color}`} />
                  </div>
                </div>
                <div>
                  <p className="text-gray-300 text-sm mb-1">{stat.title}</p>
                  <h3 className="text-2xl font-bold text-white">{stat.value}</h3>
                  {stat.description && (
                    <p className="text-purple-300 text-sm mt-1">{stat.description}</p>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Welcome Card for New Users */}
          <div className="mb-8">
            <div className="bg-gradient-to-r from-purple-900/50 to-indigo-900/50 border border-purple-500/30 rounded-lg p-8 text-center">
              <div className="text-6xl mb-4">🚀</div>
              <h3 className="text-2xl font-bold text-white mb-4">
                {t('dashboard.welcome.title', 'Welcome to Your Dashboard!')}
              </h3>
              <p className="text-purple-200 mb-6 max-w-2xl mx-auto">
                {t('dashboard.welcome.description', 'Start your entrepreneurial journey by creating your first business idea. Our AI will help you develop and refine it.')}
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <a
                  href="/dashboard/business-ideas/new"
                  className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors"
                >
                  {t('dashboard.welcome.createIdea', 'Create Your First Business Idea')}
                </a>
                <a
                  href="/dashboard/templates"
                  className="px-6 py-3 bg-transparent border border-purple-500 text-purple-300 hover:bg-purple-500/20 rounded-lg font-medium transition-colors"
                >
                  {t('dashboard.welcome.browseTemplates', 'Browse Templates')}
                </a>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-white mb-6">
              {t('dashboard.quickActions.title', 'Quick Actions')}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {quickActions.map((action, index) => (
                <a
                  key={index}
                  href={action.href}
                  className={`${action.color} text-white rounded-lg p-6 transition-colors block group`}
                >
                  <div className="flex items-center mb-4">
                    <action.icon className="w-8 h-8 mr-3" />
                    <h3 className="text-lg font-semibold">{action.title}</h3>
                  </div>
                  <p className="text-white/80 text-sm">{action.description}</p>
                </a>
              ))}
            </div>
          </div>

          {/* Simple Activity Section */}
          <div className="mb-8">
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">
                {t('dashboard.recentActivity', 'Recent Activity')}
              </h3>
              <p className="text-gray-300">
                {t('dashboard.noActivity', 'No recent activity. Start by creating your first business idea!')}
              </p>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default UserDashboard;
