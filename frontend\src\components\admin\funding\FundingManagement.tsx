import React, { useState } from 'react';
import { Search, Plus, Edit, Trash2, Eye, Filter, RefreshCw, DollarSign, TrendingUp, Users, CheckCircle, XCircle } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { AdvancedFilter, BulkActions } from '../common';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { 
  useFundingOpportunitiesList, 
  useCreateFundingOpportunity, 
  useUpdateFundingOpportunity, 
  useDeleteFundingOpportunity 
} from '../../../hooks/useFunding';
import { FundingOpportunity } from '../../../services/incubatorApi';
import { Alert, Button } from '../../ui';
import FundingOpportunityModal from './components/FundingOpportunityModal';
import FundingOpportunityDeleteModal from './components/FundingOpportunityDeleteModal';

interface FilterValue {
  status?: string;
  funding_type?: string;
  min_amount?: number;
  max_amount?: number;
  search?: string;
}

const FundingManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  // State management
  const [selectedOpportunities, setSelectedOpportunities] = useState<number[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedOpportunity, setSelectedOpportunity] = useState<FundingOpportunity | null>(null);
  const [filters, setFilters] = useState<FilterValue>({});
  const [searchTerm, setSearchTerm] = useState('');

  // API hooks
  const { data: opportunitiesData, isLoading, error, refetch } = useFundingOpportunitiesList(filters);
  const createOpportunity = useCreateFundingOpportunity();
  const updateOpportunity = useUpdateFundingOpportunity();
  const deleteOpportunity = useDeleteFundingOpportunity();

  const opportunities = opportunitiesData?.results || [];

  // Filter options
  const filterOptions = [
    {
      key: 'status',
      label: t('admin.status', 'Status'),
      type: 'select' as const,
      options: [
        { value: 'active', label: t('funding.status.active', 'Active') },
        { value: 'closed', label: t('funding.status.closed', 'Closed') },
        { value: 'draft', label: t('funding.status.draft', 'Draft') }
      ]
    },
    {
      key: 'funding_type',
      label: t('funding.type', 'Funding Type'),
      type: 'select' as const,
      options: [
        { value: 'seed', label: t('funding.type.seed', 'Seed') },
        { value: 'series_a', label: t('funding.type.seriesA', 'Series A') },
        { value: 'series_b', label: t('funding.type.seriesB', 'Series B') },
        { value: 'grant', label: t('funding.type.grant', 'Grant') },
        { value: 'loan', label: t('funding.type.loan', 'Loan') }
      ]
    },
    {
      key: 'search',
      label: t('common.search', 'Search'),
      type: 'text' as const,
      placeholder: t('funding.searchPlaceholder', 'Search funding opportunities...')
    }
  ];

  // Bulk actions
  const bulkActions = [
    {
      id: 'activate',
      label: t('funding.activateSelected', 'Activate Selected'),
      icon: CheckCircle,
      variant: 'primary' as const,
      action: async (ids: number[]) => {
        for (const id of ids) {
          await updateOpportunity.mutateAsync({ id, data: { status: 'active' } });
        }
        setSelectedOpportunities([]);
      }
    },
    {
      id: 'deactivate',
      label: t('funding.deactivateSelected', 'Deactivate Selected'),
      icon: XCircle,
      variant: 'secondary' as const,
      action: async (ids: number[]) => {
        for (const id of ids) {
          await updateOpportunity.mutateAsync({ id, data: { status: 'closed' } });
        }
        setSelectedOpportunities([]);
      }
    },
    {
      id: 'delete',
      label: t('common.delete', 'Delete'),
      icon: Trash2,
      variant: 'danger' as const,
      action: async (ids: number[]) => {
        for (const id of ids) {
          await deleteOpportunity.mutateAsync(id);
        }
        setSelectedOpportunities([]);
      }
    }
  ];

  // Event handlers
  const handleCreateOpportunity = () => {
    setSelectedOpportunity(null);
    setShowCreateModal(true);
  };

  const handleEditOpportunity = (opportunity: FundingOpportunity) => {
    setSelectedOpportunity(opportunity);
    setShowEditModal(true);
  };

  const handleDeleteOpportunity = (opportunity: FundingOpportunity) => {
    setSelectedOpportunity(opportunity);
    setShowDeleteModal(true);
  };

  const handleFilterChange = (newFilters: FilterValue) => {
    setFilters(newFilters);
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setFilters(prev => ({ ...prev, search: term }));
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { color: 'bg-green-500', label: t('funding.status.active', 'Active') },
      closed: { color: 'bg-red-500', label: t('funding.status.closed', 'Closed') },
      draft: { color: 'bg-gray-500', label: t('funding.status.draft', 'Draft') }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${config.color}`}>
        {config.label}
      </span>
    );
  };

  const getFundingTypeBadge = (type: string) => {
    const typeConfig = {
      seed: { color: 'bg-blue-500', label: t('funding.type.seed', 'Seed') },
      series_a: { color: 'bg-purple-500', label: t('funding.type.seriesA', 'Series A') },
      series_b: { color: 'bg-indigo-500', label: t('funding.type.seriesB', 'Series B') },
      grant: { color: 'bg-green-500', label: t('funding.type.grant', 'Grant') },
      loan: { color: 'bg-orange-500', label: t('funding.type.loan', 'Loan') }
    };

    const config = typeConfig[type as keyof typeof typeConfig] || { color: 'bg-gray-500', label: type };
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${config.color}`}>
        {config.label}
      </span>
    );
  };

  if (error) {
    return (
      <DashboardLayout currentPage="funding">
        <Alert variant="error">
          {t('common.error.loadFailed', 'Failed to load data. Please try again.')}
        </Alert>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout currentPage="funding">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-white">
              {t('funding.management', 'Funding Management')}
            </h1>
            <p className="text-gray-400 mt-1">
              {t('funding.managementDescription', 'Manage funding opportunities and investments')}
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              onClick={() => refetch()}
              variant="secondary"
              size="sm"
              disabled={isLoading}
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              {t('common.refresh', 'Refresh')}
            </Button>
            <Button onClick={handleCreateOpportunity} size="sm">
              <Plus className="w-4 h-4 mr-2" />
              {t('funding.createOpportunity', 'Create Opportunity')}
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-indigo-900/50 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                <DollarSign className="w-5 h-5 text-green-400" />
              </div>
              <div>
                <div className="text-gray-400 text-sm">{t('funding.totalFunding', 'Total Funding')}</div>
                <div className="text-white font-bold">$2.5M</div>
              </div>
            </div>
          </div>
          <div className="bg-indigo-900/50 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-blue-400" />
              </div>
              <div>
                <div className="text-gray-400 text-sm">{t('funding.activeOpportunities', 'Active Opportunities')}</div>
                <div className="text-white font-bold">{opportunities.filter(o => o.status === 'active').length}</div>
              </div>
            </div>
          </div>
          <div className="bg-indigo-900/50 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-purple-500/20 flex items-center justify-center">
                <Users className="w-5 h-5 text-purple-400" />
              </div>
              <div>
                <div className="text-gray-400 text-sm">{t('funding.totalInvestors', 'Total Investors')}</div>
                <div className="text-white font-bold">45</div>
              </div>
            </div>
          </div>
          <div className="bg-indigo-900/50 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-orange-500/20 flex items-center justify-center">
                <CheckCircle className="w-5 h-5 text-orange-400" />
              </div>
              <div>
                <div className="text-gray-400 text-sm">{t('funding.successfulDeals', 'Successful Deals')}</div>
                <div className="text-white font-bold">12</div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-indigo-900/50 rounded-lg p-4">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder={t('funding.searchPlaceholder', 'Search funding opportunities...')}
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                />
              </div>
            </div>
            <AdvancedFilter
              filters={filterOptions}
              values={filters}
              onChange={handleFilterChange}
            />
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedOpportunities.length > 0 && (
          <BulkActions
            selectedCount={selectedOpportunities.length}
            actions={bulkActions}
            onClearSelection={() => setSelectedOpportunities([])}
          />
        )}

        {/* Funding Opportunities Table */}
        <div className="bg-indigo-900/50 rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-indigo-800/50">
                <tr>
                  <th className="px-4 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedOpportunities.length === opportunities.length && opportunities.length > 0}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedOpportunities(opportunities.map(o => o.id));
                        } else {
                          setSelectedOpportunities([]);
                        }
                      }}
                      className="rounded border-gray-600 bg-indigo-800 text-purple-500 focus:ring-purple-500"
                    />
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('common.title', 'Title')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('funding.type', 'Type')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('funding.amount', 'Amount')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('admin.status', 'Status')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('funding.applications', 'Applications')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('funding.deadline', 'Deadline')}
                  </th>
                  <th className="px-4 py-3 text-right text-gray-300 font-medium">
                    {t('common.actions', 'Actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-indigo-800/50">
                {isLoading ? (
                  <tr>
                    <td colSpan={8} className="px-4 py-8 text-center text-gray-400">
                      {t('common.loading', 'Loading...')}
                    </td>
                  </tr>
                ) : opportunities.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="px-4 py-8 text-center text-gray-400">
                      {t('funding.noOpportunities', 'No funding opportunities found')}
                    </td>
                  </tr>
                ) : (
                  opportunities.map((opportunity) => (
                    <tr key={opportunity.id} className="hover:bg-indigo-800/30">
                      <td className="px-4 py-3">
                        <input
                          type="checkbox"
                          checked={selectedOpportunities.includes(opportunity.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedOpportunities(prev => [...prev, opportunity.id]);
                            } else {
                              setSelectedOpportunities(prev => prev.filter(id => id !== opportunity.id));
                            }
                          }}
                          className="rounded border-gray-600 bg-indigo-800 text-purple-500 focus:ring-purple-500"
                        />
                      </td>
                      <td className="px-4 py-3">
                        <div className="text-white font-medium">{opportunity.title}</div>
                        <div className="text-gray-400 text-sm line-clamp-1">
                          {opportunity.description}
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        {getFundingTypeBadge(opportunity.funding_type)}
                      </td>
                      <td className="px-4 py-3">
                        <div className="text-white font-medium">
                          ${opportunity.min_amount?.toLocaleString()} - ${opportunity.max_amount?.toLocaleString()}
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        {getStatusBadge(opportunity.status)}
                      </td>
                      <td className="px-4 py-3">
                        <div className="text-gray-300">
                          {opportunity.applications_count || 0} applications
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div className="text-gray-300">
                          {opportunity.deadline ? new Date(opportunity.deadline).toLocaleDateString() : 'No deadline'}
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex justify-end gap-2">
                          <Button
                            onClick={() => handleEditOpportunity(opportunity)}
                            variant="secondary"
                            size="sm"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            onClick={() => handleDeleteOpportunity(opportunity)}
                            variant="danger"
                            size="sm"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Modals */}
        <FundingOpportunityModal
          isOpen={showCreateModal || showEditModal}
          onClose={() => {
            setShowCreateModal(false);
            setShowEditModal(false);
            setSelectedOpportunity(null);
          }}
          opportunity={selectedOpportunity}
          isEditing={showEditModal}
        />

        <FundingOpportunityDeleteModal
          isOpen={showDeleteModal}
          onClose={() => {
            setShowDeleteModal(false);
            setSelectedOpportunity(null);
          }}
          opportunity={selectedOpportunity}
        />
      </div>
    </DashboardLayout>
  );
};

export default FundingManagement;
