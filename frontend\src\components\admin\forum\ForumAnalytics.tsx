import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '../dashboard';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  Users,
  MessageSquare,
  Award,
  Calendar,
  RefreshCw,
  Filter
} from 'lucide-react';
import { forumApi } from '../../../services/forumApi';
import {
  <PERSON><PERSON><PERSON> as RechartsBarChart,
  Line<PERSON>hart as RechartsLineChart,
  Bar,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>ie<PERSON><PERSON>,
  <PERSON>,
  Cell
} from 'recharts';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe'];

const ForumAnalytics: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [activeTab, setActiveTab] = useState<string>('overview');
  const [loading, setLoading] = useState<boolean>(true);
  const [overviewData, setOverviewData] = useState<any>(null);
  const [activityData, setActivityData] = useState<any>(null);
  const [contributorsData, setContributorsData] = useState<any>(null);
  const [popularThreadsData, setPopularThreadsData] = useState<any>(null);
  const [engagementData, setEngagementData] = useState<any>(null);
  const [period, setPeriod] = useState<string>('day');
  const [timeRange, setTimeRange] = useState<number>(30);
  const [popularPeriod, setPopularPeriod] = useState<string>('month');

  useEffect(() => {
    fetchOverviewData();
  }, []);

  useEffect(() => {
    if (activeTab === 'activity') {
      fetchActivityData();
    } else if (activeTab === 'contributors') {
      fetchContributorsData();
    } else if (activeTab === 'popular') {
      fetchPopularThreadsData();
    } else if (activeTab === 'engagement') {
      fetchEngagementData();
    }
  }, [activeTab, period, timeRange, popularPeriod]);

  const fetchOverviewData = async () => {
    setLoading(true);
    try {
      const data = await forumApi.getForumAnalytics('overview');
      setOverviewData(data);
    } catch (error) {
      console.error('Error fetching overview data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchActivityData = async () => {
    setLoading(true);
    try {
      const data = await forumApi.getForumAnalytics('activity', { period, days: timeRange });
      setActivityData(data);
    } catch (error) {
      console.error('Error fetching activity data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchContributorsData = async () => {
    setLoading(true);
    try {
      const data = await forumApi.getForumAnalytics('contributors', { limit: 10 });
      setContributorsData(data);
    } catch (error) {
      console.error('Error fetching contributors data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchPopularThreadsData = async () => {
    setLoading(true);
    try {
      const data = await forumApi.getForumAnalytics('popular_threads', { period: popularPeriod, limit: 10 });
      setPopularThreadsData(data);
    } catch (error) {
      console.error('Error fetching popular threads data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchEngagementData = async () => {
    setLoading(true);
    try {
      const data = await forumApi.getForumAnalytics('engagement');
      setEngagementData(data);
    } catch (error) {
      console.error('Error fetching engagement data:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderOverviewTab = () => {
    if (!overviewData) return <div className="text-center py-8">t("admin.no.data.available", t("common.noDataAvailable", "No data available"))</div>;

    // Prepare data for charts
    const categoryData = overviewData.active_categories.map((cat: any) => ({
      name: cat.name,
      threads: cat.thread_count,
      posts: cat.post_count,
      value: cat.total_activity
    }));

    const topicData = overviewData.active_topics.map((topic: any) => ({
      name: topic.title,
      threads: topic.thread_count,
      posts: topic.post_count,
      value: topic.total_activity
    }));

    return (
      <div className="space-y-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-lg font-semibold">t("admin.total.threads", "Total Threads")</h3>
              <MessageSquare size={20} className="text-purple-400" />
            </div>
            <div className="text-3xl font-bold mt-2">{overviewData.total_stats.threads}</div>
            <p className="text-sm text-gray-400 mt-1">
              <span className={overviewData.weekly_stats.thread_growth > 0 ? "text-green-400" : "text-red-400"}>
                {overviewData.weekly_stats.thread_growth > 0 ? "+" : ""}
                {overviewData.weekly_stats.thread_growth.toFixed(1)}%
              </span> this week
            </p>
          </div>

          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-lg font-semibold">t("admin.total.posts", "Total Posts")</h3>
              <MessageSquare size={20} className="text-purple-400" />
            </div>
            <div className="text-3xl font-bold mt-2">{overviewData.total_stats.posts}</div>
            <p className="text-sm text-gray-400 mt-1">
              <span className={overviewData.weekly_stats.post_growth > 0 ? "text-green-400" : "text-red-400"}>
                {overviewData.weekly_stats.post_growth > 0 ? "+" : ""}
                {overviewData.weekly_stats.post_growth.toFixed(1)}%
              </span> this week
            </p>
          </div>

          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-lg font-semibold">t("admin.active.users", "Active Users")</h3>
              <Users size={20} className="text-purple-400" />
            </div>
            <div className="text-3xl font-bold mt-2">{overviewData.weekly_stats.active_users}</div>
            <p className="text-sm text-gray-400 mt-1">
              Out of {overviewData.total_stats.users} total users
            </p>
          </div>
        </div>

        {/* Active Categories Chart */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <h3 className="text-xl font-semibold mb-4">t("admin.most.active.categories", "Most Active Categories")</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsBarChart
                data={categoryData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#444" />
                <XAxis dataKey="name" stroke="#aaa" />
                <YAxis stroke="#aaa" />
                <Tooltip
                  contentStyle={{ backgroundColor: '#1e1e3f', borderColor: '#444', color: '#fff' }}
                />
                <Legend />
                <Bar dataKey="threads" name="Threads" fill="#8884d8" />
                <Bar dataKey="posts" name="Posts" fill="#82ca9d" />
              </RechartsBarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Active Topics Chart */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <h3 className="text-xl font-semibold mb-4">t("admin.most.active.topics", "Most Active Topics")</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsBarChart
                data={topicData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                layout="vertical"
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#444" />
                <XAxis type="number" stroke="#aaa" />
                <YAxis dataKey="name" type="category" stroke="#aaa" width={150} />
                <Tooltip
                  contentStyle={{ backgroundColor: '#1e1e3f', borderColor: '#444', color: '#fff' }}
                />
                <Legend />
                <Bar dataKey="threads" name="Threads" fill="#8884d8" />
                <Bar dataKey="posts" name="Posts" fill="#82ca9d" />
              </RechartsBarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    );
  };

  const renderActivityTab = () => {
    if (!activityData) return <div className="text-center py-8">t("admin.no.data.available", t("common.noDataAvailable", "No data available"))</div>;

    return (
      <div className="space-y-6">
        {/* Controls */}
        <div className={`flex flex-wrap gap-4 items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <label className="block text-sm font-medium mb-1">t("admin.time.period", "Time Period")</label>
            <select
              value={period}
              onChange={(e) => setPeriod(e.target.value)}
              className="bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="day">t("admin.daily", "Daily")</option>
              <option value="week">t("admin.weekly", "Weekly")</option>
              <option value="month">t("admin.monthly", "Monthly")</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">t("admin.time.range", "Time Range")</label>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(parseInt(e.target.value))}
              className="bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
              <option value="180">Last 180 days</option>
            </select>
          </div>
          <button
            onClick={fetchActivityData}
            className="mt-6 p-2 bg-indigo-900/30 border border-indigo-800/50 rounded-lg hover:bg-indigo-800/30"
            title={t("common.refresh")}
          >
            <RefreshCw size={18} />
          </button>
        </div>

        {/* Thread Activity Chart */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <h3 className="text-xl font-semibold mb-4">t("admin.thread.activity", "Thread Activity")</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsLineChart
                data={activityData.thread_activity}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#444" />
                <XAxis dataKey="period" stroke="#aaa" />
                <YAxis stroke="#aaa" />
                <Tooltip
                  contentStyle={{ backgroundColor: '#1e1e3f', borderColor: '#444', color: '#fff' }}
                />
                <Legend />
                <Line type="monotone" dataKey="count" name="Threads" stroke="#8884d8" activeDot={{ r: 8 }} />
              </RechartsLineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Post Activity Chart */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <h3 className="text-xl font-semibold mb-4">t("admin.post.activity", "Post Activity")</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsLineChart
                data={activityData.post_activity}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#444" />
                <XAxis dataKey="period" stroke="#aaa" />
                <YAxis stroke="#aaa" />
                <Tooltip
                  contentStyle={{ backgroundColor: '#1e1e3f', borderColor: '#444', color: '#fff' }}
                />
                <Legend />
                <Line type="monotone" dataKey="count" name="Posts" stroke="#82ca9d" activeDot={{ r: 8 }} />
              </RechartsLineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* User Activity Chart */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <h3 className="text-xl font-semibold mb-4">t("admin.user.activity", "User Activity")</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsLineChart
                data={activityData.user_activity}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#444" />
                <XAxis dataKey="period" stroke="#aaa" />
                <YAxis stroke="#aaa" />
                <Tooltip
                  contentStyle={{ backgroundColor: '#1e1e3f', borderColor: '#444', color: '#fff' }}
                />
                <Legend />
                <Line type="monotone" dataKey="count" name="Active Users" stroke="#ffc658" activeDot={{ r: 8 }} />
              </RechartsLineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    );
  };

  const renderContributorsTab = () => {
    if (!contributorsData || contributorsData.length === 0)
      return <div className="text-center py-8">t("admin.no.data.available", t("common.noDataAvailable", "No data available"))</div>;

    return (
      <div className="space-y-6">
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <h3 className="text-xl font-semibold mb-4">t("admin.top.contributors", "Top Contributors")</h3>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-indigo-800/50">
                  <th className={`px-4 py-2  ${isRTL ? "text-right" : "text-left"}`}>t("admin.rank", "Rank")</th>
                  <th className={`px-4 py-2  ${isRTL ? "text-right" : "text-left"}`}>t("admin.user", "User")</th>
                  <th className={`px-4 py-2  ${isRTL ? "text-right" : "text-left"}`}>t("admin.level", "Level")</th>
                  <th className="px-4 py-2 text-right">t("admin.points", "Points")</th>
                  <th className="px-4 py-2 text-right">t("admin.threads", "Threads")</th>
                  <th className="px-4 py-2 text-right">t("admin.posts", "Posts")</th>
                  <th className="px-4 py-2 text-right">t("admin.solutions", "Solutions")</th>
                  <th className="px-4 py-2 text-right">t("admin.likes", "Likes")</th>
                </tr>
              </thead>
              <tbody>
                {contributorsData.map((user: any, index: number) => (
                  <tr key={user.user_id} className="border-b border-indigo-800/30 hover:bg-indigo-800/20">
                    <td className={`px-4 py-3  ${isRTL ? "text-right" : "text-left"}`}>{index + 1}</td>
                    <td className={`px-4 py-3  font-medium ${isRTL ? "text-right" : "text-left"}`}>{user.username}</td>
                    <td className={`px-4 py-3  ${isRTL ? "text-right" : "text-left"}`}>
                      <span className={`px-2 py-1 rounded text-xs ${
                        user.level === 'Newcomer' ? 'bg-gray-700/50' :
                        user.level === 'Contributor' ? 'bg-blue-700/50' :
                        user.level === 'Active Member' ? 'bg-green-700/50' :
                        user.level === 'Expert' ? 'bg-yellow-700/50' :
                        user.level === 'Mentor' ? 'bg-orange-700/50' :
                        'bg-purple-700/50'}
                      }`}>
                        {user.level}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-right font-bold">{user.points}</td>
                    <td className="px-4 py-3 text-right">{user.threads_created}</td>
                    <td className="px-4 py-3 text-right">{user.posts_created}</td>
                    <td className="px-4 py-3 text-right">{user.solutions_provided}</td>
                    <td className="px-4 py-3 text-right">{user.likes_received}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <h3 className="text-xl font-semibold mb-4">t("admin.contribution.distribution", "Contribution Distribution")</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsPieChart>
                <Pie
                  data={contributorsData.map((user: any) => ({
                    name: user.username,
                    value: user.points
                  }))}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {contributorsData.map((entry: any, index: number) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{ backgroundColor: '#1e1e3f', borderColor: '#444', color: '#fff' }}
                />
                <Legend />
              </RechartsPieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    );
  };

  const renderPopularThreadsTab = () => {
    if (!popularThreadsData || popularThreadsData.length === 0)
      return <div className="text-center py-8">t("admin.no.data.available", t("common.noDataAvailable", "No data available"))</div>;

    return (
      <div className="space-y-6">
        {/* Controls */}
        <div className={`flex flex-wrap gap-4 items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <label className="block text-sm font-medium mb-1">t("admin.time.period", "Time Period")</label>
            <select
              value={popularPeriod}
              onChange={(e) => setPopularPeriod(e.target.value)}
              className="bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="">t("admin.all.time", "All Time")</option>
              <option value="week">t("admin.last.week", "Last Week")</option>
              <option value="month">t("admin.last.month", "Last Month")</option>
              <option value="year">t("admin.last.year", "Last Year")</option>
            </select>
          </div>
          <button
            onClick={fetchPopularThreadsData}
            className="mt-6 p-2 bg-indigo-900/30 border border-indigo-800/50 rounded-lg hover:bg-indigo-800/30"
            title={t("common.refresh")}
          >
            <RefreshCw size={18} />
          </button>
        </div>

        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <h3 className="text-xl font-semibold mb-4">t("admin.popular.threads", "Popular Threads")</h3>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-indigo-800/50">
                  <th className={`px-4 py-2  ${isRTL ? "text-right" : "text-left"}`}>t("admin.rank", "Rank")</th>
                  <th className={`px-4 py-2  ${isRTL ? "text-right" : "text-left"}`}>t("admin.title", t("common.title", "Title"))</th>
                  <th className={`px-4 py-2  ${isRTL ? "text-right" : "text-left"}`}>t("admin.author", "Author")</th>
                  <th className={`px-4 py-2  ${isRTL ? "text-right" : "text-left"}`}>t("admin.category", t("common.category", "Category"))</th>
                  <th className="px-4 py-2 text-right">t("admin.views", "Views")</th>
                  <th className="px-4 py-2 text-right">t("admin.posts", "Posts")</th>
                  <th className="px-4 py-2 text-right">t("admin.likes", "Likes")</th>
                  <th className="px-4 py-2 text-right">t("admin.score", "Score")</th>
                </tr>
              </thead>
              <tbody>
                {popularThreadsData.map((thread: any, index: number) => (
                  <tr key={thread.id} className="border-b border-indigo-800/30 hover:bg-indigo-800/20">
                    <td className={`px-4 py-3  ${isRTL ? "text-right" : "text-left"}`}>{index + 1}</td>
                    <td className={`px-4 py-3  font-medium ${isRTL ? "text-right" : "text-left"}`}>{thread.title}</td>
                    <td className={`px-4 py-3  ${isRTL ? "text-right" : "text-left"}`}>{thread.author}</td>
                    <td className={`px-4 py-3  ${isRTL ? "text-right" : "text-left"}`}>{thread.category}</td>
                    <td className="px-4 py-3 text-right">{thread.views}</td>
                    <td className="px-4 py-3 text-right">{thread.post_count}</td>
                    <td className="px-4 py-3 text-right">{thread.like_count}</td>
                    <td className="px-4 py-3 text-right font-bold">{thread.activity_score}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <h3 className="text-xl font-semibold mb-4">t("admin.activity.distribution", "Activity Distribution")</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsBarChart
                data={popularThreadsData.map((thread: any) => ({
                  name: thread.title.length > 30 ? thread.title.substring(0, 30) + '...' : thread.title,
                  views: thread.views,
                  posts: thread.post_count,
                  likes: thread.like_count
                }))}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#444" />
                <XAxis dataKey="name" stroke="#aaa" />
                <YAxis stroke="#aaa" />
                <Tooltip
                  contentStyle={{ backgroundColor: '#1e1e3f', borderColor: '#444', color: '#fff' }}
                />
                <Legend />
                <Bar dataKey="views" name="Views" fill="#8884d8" />
                <Bar dataKey="posts" name="Posts" fill="#82ca9d" />
                <Bar dataKey="likes" name="Likes" fill="#ffc658" />
              </RechartsBarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    );
  };

  const renderEngagementTab = () => {
    if (!engagementData) return <div className="text-center py-8">t("admin.no.data.available", t("common.noDataAvailable", "No data available"))</div>;

    // Prepare data for charts
    const userEngagementData = [
      { name: t("admin.participation.rate", "Participation Rate"), value: engagementData.participation_rate },
      { name: t("admin.nonparticipants", "Non-Participants"), value: 100 - engagementData.participation_rate }
    ];

    const subscriptionData = [
      { name: t("admin.subscribed.users", "Subscribed Users"), value: engagementData.subscription_rate },
      { name: t("admin.nonsubscribed", "Non-Subscribed"), value: 100 - engagementData.subscription_rate }
    ];

    return (
      <div className="space-y-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-lg font-semibold">t("admin.forum.users", "Forum Users")</h3>
              <Users size={20} className="text-purple-400" />
            </div>
            <div className="text-3xl font-bold mt-2">{engagementData.total_forum_users}</div>
            <p className="text-sm text-gray-400 mt-1">
              Out of {engagementData.total_users} total users
            </p>
          </div>

          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-lg font-semibold">t("admin.avg.posts.per", "Avg. Posts Per User")</h3>
              <MessageSquare size={20} className="text-purple-400" />
            </div>
            <div className="text-3xl font-bold mt-2">{engagementData.avg_posts_per_user.toFixed(1)}</div>
            <p className="text-sm text-gray-400 mt-1">
              {engagementData.avg_threads_per_user.toFixed(1)} threads per user
            </p>
          </div>

          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-lg font-semibold">t("admin.subscribed.users", "Subscribed Users")</h3>
              <Calendar size={20} className="text-purple-400" />
            </div>
            <div className="text-3xl font-bold mt-2">{engagementData.users_with_subscriptions}</div>
            <p className="text-sm text-gray-400 mt-1">
              {engagementData.subscription_rate.toFixed(1)}% subscription rate
            </p>
          </div>
        </div>

        {/* Participation Rate Chart */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            <h3 className="text-xl font-semibold mb-4">t("admin.user.participation.rate", "User Participation Rate")</h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsPieChart>
                  <Pie
                    data={userEngagementData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, value }) => `${name}: ${value.toFixed(1)}%`}
                  >
                    <Cell fill="#8884d8" />
                    <Cell fill="#444" />
                  </Pie>
                  <Tooltip
                    contentStyle={{ backgroundColor: '#1e1e3f', borderColor: '#444', color: '#fff' }}
                  />
                </RechartsPieChart>
              </ResponsiveContainer>
            </div>
          </div>

          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            <h3 className="text-xl font-semibold mb-4">t("admin.subscription.rate", "Subscription Rate")</h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsPieChart>
                  <Pie
                    data={subscriptionData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#82ca9d"
                    dataKey="value"
                    label={({ name, value }) => `${name}: ${value.toFixed(1)}%`}
                  >
                    <Cell fill="#82ca9d" />
                    <Cell fill="#444" />
                  </Pie>
                  <Tooltip
                    contentStyle={{ backgroundColor: '#1e1e3f', borderColor: '#444', color: '#fff' }}
                  />
                </RechartsPieChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <DashboardLayout currentPage="forum-analytics">
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">t("admin.forum.analytics", "Forum Analytics")</h1>

        {/* Tabs */}
        <div className={`flex flex-wrap gap-2 mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            className={`px-4 py-2 rounded-lg flex items-center gap-2 ${
              activeTab === 'overview'
                ? 'bg-purple-700/50 text-white'
                : 'bg-indigo-900/30 hover:bg-indigo-800/30 text-gray-300'}
            }`}
            onClick={() => setActiveTab('overview')}
          >
            <PieChart size={16} />
            Overview
          </button>
          <button
            className={`px-4 py-2 rounded-lg flex items-center gap-2 ${
              activeTab === 'activity'
                ? 'bg-purple-700/50 text-white'
                : 'bg-indigo-900/30 hover:bg-indigo-800/30 text-gray-300'}
            }`}
            onClick={() => setActiveTab('activity')}
          >
            <LineChart size={16} />
            Activity
          </button>
          <button
            className={`px-4 py-2 rounded-lg flex items-center gap-2 ${
              activeTab === 'contributors'
                ? 'bg-purple-700/50 text-white'
                : 'bg-indigo-900/30 hover:bg-indigo-800/30 text-gray-300'}
            }`}
            onClick={() => setActiveTab('contributors')}
          >
            <Users size={16} />
            Contributors
          </button>
          <button
            className={`px-4 py-2 rounded-lg flex items-center gap-2 ${
              activeTab === 'popular'
                ? 'bg-purple-700/50 text-white'
                : 'bg-indigo-900/30 hover:bg-indigo-800/30 text-gray-300'}
            }`}
            onClick={() => setActiveTab('popular')}
          >
            <TrendingUp size={16} />
            Popular Threads
          </button>
          <button
            className={`px-4 py-2 rounded-lg flex items-center gap-2 ${
              activeTab === 'engagement'
                ? 'bg-purple-700/50 text-white'
                : 'bg-indigo-900/30 hover:bg-indigo-800/30 text-gray-300'}
            }`}
            onClick={() => setActiveTab('engagement')}
          >
            <BarChart size={16} />
            Engagement
          </button>
        </div>

        {/* Loading State */}
        {loading && (
          <div className={`flex justify-center items-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
          </div>
        )}

        {/* Tab Content */}
        {!loading && (
          <>
            {activeTab === 'overview' && renderOverviewTab()}
            {activeTab === 'activity' && renderActivityTab()}
            {activeTab === 'contributors' && renderContributorsTab()}
            {activeTab === 'popular' && renderPopularThreadsTab()}
            {activeTab === 'engagement' && renderEngagementTab()}
          </>
        )}
      </div>
    </DashboardLayout>
  );
};

export default ForumAnalytics;
