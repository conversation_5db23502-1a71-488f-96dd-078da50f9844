import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAppSelector } from '../../store/hooks';
import { RouteConfig, validateRouteAccess } from '../../routes/routeConfig';
import { isSuperAdmin } from '../../utils/roleBasedRouting';
import { UserRole, PermissionLevel } from '../../routes/routeConfig';

interface RoleRouteProps {
  config: RouteConfig;
  children: React.ReactNode;
}

/**
 * RoleRoute component that handles role-based access control for routes
 */
const RoleRoute: React.FC<RoleRouteProps> = ({ config, children }) => {
  const location = useLocation();
  const { user, isAuthenticated } = useAppSelector((state) => state.auth);

  // Extract user roles using unified role manager
  const extractUserRoles = (user: any): UserRole[] => {
    // Import here to avoid circular dependencies
    const { createRoleManager } = require('../../utils/unifiedRoleManager');
    return createRoleManager(user).getUserRoles() as UserRole[];
  };

  const extractUserPermissions = (user: any): PermissionLevel[] => {
    if (!user) return [];
    
    const permissions: PermissionLevel[] = ['read']; // Everyone gets read by default
    
    if (user.is_admin || isSuperAdmin(user)) {
      permissions.push('write', 'moderate', 'admin');
    }
    
    if (isSuperAdmin(user)) {
      permissions.push('super_admin');
    }
    
    if (user.is_moderator) {
      permissions.push('moderate');
    }
    
    if (user.is_mentor || user.is_investor) {
      permissions.push('write');
    }
    
    return permissions;
  };

  // Check if user has access to this route
  const userRoles = extractUserRoles(user);
  const userPermissions = extractUserPermissions(user);
  const isAdmin = user?.is_admin === true;
  const isSuperAdminUser = user ? isSuperAdmin(user) : false;

  const hasAccess = validateRouteAccess(
    config,
    userRoles,
    userPermissions,
    isAuthenticated,
    isAdmin,
    isSuperAdminUser
  );

  // If user doesn't have access, redirect to appropriate page
  if (!hasAccess) {
    const redirectTo = config.redirectTo || '/login';
    
    // If not authenticated, redirect to login with return URL
    if (!isAuthenticated) {
      return <Navigate to={`/login?returnUrl=${encodeURIComponent(location.pathname)}`} replace />;
    }
    
    // If authenticated but insufficient permissions, redirect to configured path
    return <Navigate to={redirectTo} replace />;
  }

  // User has access, render the children
  return <>{children}</>;
};

export default RoleRoute;
