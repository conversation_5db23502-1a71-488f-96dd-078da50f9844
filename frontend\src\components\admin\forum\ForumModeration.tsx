import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '../dashboard';
import {
  Search,
  Filter,
  MessageSquare,
  CheckCircle,
  X,
  AlertTriangle,
  Eye,
  Pin,
  Lock,
  Unlock,
  RefreshCw,
  CheckSquare,
  Square
} from 'lucide-react';
import { forumApi, ForumThread } from '../../../services/forumApi';
import { formatDistanceToNow } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { RTLText } from '../../common';

const ForumModeration: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [threads, setThreads] = useState<ForumThread[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('pending');
  const [moderatingThread, setModeratingThread] = useState<number | null>(null);
  const [moderationComment, setModerationComment] = useState('');
  const [selectedThreads, setSelectedThreads] = useState<number[]>([]);
  const [bulkModeratingStatus, setBulkModeratingStatus] = useState<'approved' | 'rejected' | null>(null);

  useEffect(() => {
    fetchThreads();
  }, [statusFilter]);

  const fetchThreads = async () => {
    setLoading(true);
    try {
      const fetchedThreads = await forumApi.getThreads();
      // Filter threads based on moderation status
      const filteredThreads = fetchedThreads.filter(thread =>
        statusFilter === 'all' ? true : thread.moderation_status === statusFilter
      );
      setThreads(filteredThreads);
    } catch (error) {
      console.error('Error fetching threads:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (threadId: number) => {
    setModeratingThread(threadId);
    try {
      await forumApi.moderateThread(threadId, 'approved', moderationComment);
      // Update the thread in the list
      setThreads(threads.map(thread =>
        thread.id === threadId
          ? { ...thread, moderation_status: 'approved', moderation_comment: moderationComment }
          : thread
      ));
      setModerationComment('');
    } catch (error) {
      console.error('Error approving thread:', error);
    } finally {
      setModeratingThread(null);
    }
  };

  const handleReject = async (threadId: number) => {
    if (!moderationComment.trim()) {
      alert(t('admin.forum.pleaseProvideReason'));
      return;
    }

    setModeratingThread(threadId);
    try {
      await forumApi.moderateThread(threadId, 'rejected', moderationComment);
      // Update the thread in the list
      setThreads(threads.map(thread =>
        thread.id === threadId
          ? { ...thread, moderation_status: 'rejected', moderation_comment: moderationComment }
          : thread
      ));
      setModerationComment('');
    } catch (error) {
      console.error('Error rejecting thread:', error);
    } finally {
      setModeratingThread(null);
    }
  };

  const handleTogglePin = async (threadId: number, isPinned: boolean) => {
    try {
      await forumApi.updateThread(threadId, { is_pinned: !isPinned });
      // Update the thread in the list
      setThreads(threads.map(thread =>
        thread.id === threadId
          ? { ...thread, is_pinned: !isPinned }
          : thread
      ));
    } catch (error) {
      console.error('Error toggling pin status:', error);
    }
  };

  const handleToggleLock = async (threadId: number, isLocked: boolean) => {
    try {
      await forumApi.updateThread(threadId, { is_locked: !isLocked });
      // Update the thread in the list
      setThreads(threads.map(thread =>
        thread.id === threadId
          ? { ...thread, is_locked: !isLocked }
          : thread
      ));
    } catch (error) {
      console.error('Error toggling lock status:', error);
    }
  };

  const handleSelectThread = (threadId: number) => {
    if (selectedThreads.includes(threadId)) {
      setSelectedThreads(selectedThreads.filter(id => id !== threadId));
    } else {
      setSelectedThreads([...selectedThreads, threadId]);
    }
  };

  const handleSelectAll = () => {
    if (selectedThreads.length === filteredThreads.length) {
      setSelectedThreads([]);
    } else {
      setSelectedThreads(filteredThreads.map(thread => thread.id));
    }
  };

  const handleBulkModerate = async (status: 'approved' | 'rejected') => {
    if (selectedThreads.length === 0) {
      alert(t('admin.forum.selectAtLeastOne'));
      return;
    }

    if (status === 'rejected' && !moderationComment.trim()) {
      alert(t('admin.forum.pleaseProvideReason'));
      return;
    }

    setBulkModeratingStatus(status);

    try {
      await forumApi.bulkModerateThreads(selectedThreads, status as 'approved' | 'rejected', moderationComment);

      // Update threads in the list
      setThreads(threads.map(thread =>
        selectedThreads.includes(thread.id)
          ? {
              ...thread,
              moderation_status: status,
              moderation_comment: moderationComment
            } as ForumThread
          : thread
      ));

      // Clear selection and comment
      setSelectedThreads([]);
      setModerationComment('');

      // Refresh the list if we're filtering by status
      if (statusFilter !== 'all') {
        fetchThreads();
      }
    } catch (error) {
      console.error('Error bulk moderating threads:', error);
    } finally {
      setBulkModeratingStatus(null);
    }
  };

  // Filter threads based on search term
  const filteredThreads = threads.filter(thread =>
    thread.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    thread.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
    thread.author.username.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <DashboardLayout currentPage="forum-moderation">
      <div className="mb-8">
        <RTLText as="h1" className="text-2xl font-bold">{t('admin.forum.title')}</RTLText>
        <RTLText as="div" className="text-gray-400 mt-1">{t('admin.forum.description')}</RTLText>
      </div>

      <div className={`flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`relative flex-1 max-w-md ${isRTL ? "flex-row-reverse" : ""}`}>
          <input
            type="text"
            placeholder={t('admin.forum.searchThreads')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 pl-10 pr-4 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
          <Search size={18} className="absolute left-3 top-2.5 text-gray-400" />
        </div>

        <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="relative">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 pl-10 pr-4 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 appearance-none"
            >
              <option value="all">{t('admin.forum.allThreads')}</option>
              <option value="pending">{t('admin.forum.pendingReview')}</option>
              <option value="approved">{t('admin.approved')}</option>
              <option value="rejected">{t('admin.rejected')}</option>
            </select>
            <Filter size={18} className="absolute left-3 top-2.5 text-gray-400" />
          </div>

          <button
            onClick={fetchThreads}
            className="p-2 bg-indigo-900/30 border border-indigo-800/50 rounded-lg hover:bg-indigo-800/30"
            title={t('admin.forum.refresh')}
          >
            <RefreshCw size={18} />
          </button>
        </div>
      </div>

      {/* Bulk Moderation Controls */}
      {filteredThreads.length > 0 && (
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50 mb-6">
          <div className={`flex flex-col md:flex-row items-start md:items-center gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={handleSelectAll}
                className={`p-2 rounded hover:bg-indigo-800/50 mr-2 ${isRTL ? "space-x-reverse" : ""}`}
                title={selectedThreads.length === filteredThreads.length ? t('admin.forum.deselectAll') : t('admin.forum.selectAll')}
              >
                {selectedThreads.length === filteredThreads.length ? (
                  <CheckSquare size={20} className="text-purple-400" />
                ) : (
                  <Square size={20} />
                )}
              </button>
              <span className="text-sm">
                {t('admin.forum.selectedCount', { selected: selectedThreads.length, total: filteredThreads.length })}
              </span>
            </div>

            <div className={`flex-1 min-w-0 ${isRTL ? "flex-row-reverse" : ""}`}>
              <input
                type="text"
                placeholder={t('admin.forum.moderationCommentPlaceholder')}
                value={moderationComment}
                onChange={(e) => setModerationComment(e.target.value)}
                className="w-full bg-indigo-800/50 border border-indigo-700/50 rounded-lg py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>

            <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => handleBulkModerate('approved')}
                disabled={selectedThreads.length === 0 || bulkModeratingStatus !== null}
                className={`px-3 py-2 bg-green-900/30 hover:bg-green-800/50 text-green-400 rounded-lg text-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed ${isRTL ? "flex-row-reverse" : ""}`}
              >
                {bulkModeratingStatus === 'approved' ? (
                  <div className={`animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-green-400 mr-1 ${isRTL ? "space-x-reverse" : ""}`}></div>
                ) : (
                  <CheckCircle size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                )}
                {t('admin.forum.approveSelected')}
              </button>

              <button
                onClick={() => handleBulkModerate('rejected')}
                disabled={selectedThreads.length === 0 || bulkModeratingStatus !== null}
                className={`px-3 py-2 bg-red-900/30 hover:bg-red-800/50 text-red-400 rounded-lg text-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed ${isRTL ? "flex-row-reverse" : ""}`}
              >
                {bulkModeratingStatus === 'rejected' ? (
                  <div className={`animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-red-400 mr-1 ${isRTL ? "space-x-reverse" : ""}`}></div>
                ) : (
                  <X size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                )}
                {t('admin.forum.rejectSelected')}
              </button>
            </div>
          </div>
        </div>
      )}

      {loading ? (
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <div className={`flex justify-center items-center h-40 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
          </div>
        </div>
      ) : filteredThreads.length === 0 ? (
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 text-center">
          <div className="text-gray-400">
            {searchTerm
              ? t('admin.forum.noThreadsFoundSearch')
              : statusFilter === 'pending'
                ? t('admin.forum.noThreadsPending')
                : t('admin.forum.noThreadsFound')}
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredThreads.map((thread) => (
            <div
              key={thread.id}
              className={`bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border ${
                thread.moderation_status === 'approved'
                  ? 'border-green-800/50'
                  : thread.moderation_status === 'rejected'
                    ? 'border-red-800/50'
                    : 'border-yellow-800/50'}
              } ${selectedThreads.includes(thread.id) ? 'ring-2 ring-purple-500' : ''}`}
            >
              <div className={`flex justify-between items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                  <button
                    onClick={() => handleSelectThread(thread.id)}
                    className={`p-2 rounded hover:bg-indigo-800/50 mr-2 mt-1 ${isRTL ? "space-x-reverse" : ""}`}
                  >
                    {selectedThreads.includes(thread.id) ? (
                      <CheckSquare size={18} className="text-purple-400" />
                    ) : (
                      <Square size={18} />
                    )}
                  </button>
                  <div>
                    <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                      <h2 className="text-xl font-semibold">{thread.title}</h2>
                      {thread.is_pinned && (
                        <Pin size={16} className={`ml-2 text-yellow-400 ${isRTL ? "space-x-reverse" : ""}`} />
                      )}
                      {thread.is_locked && (
                        <Lock size={16} className={`ml-2 text-red-400 ${isRTL ? "space-x-reverse" : ""}`} />
                      )}
                    </div>

                    <div className={`flex items-center mt-2 text-sm text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <span>{t('admin.forum.by')} {thread.author.username}</span>
                      <span className="mx-2">•</span>
                      <span>{formatDistanceToNow(new Date(thread.created_at), { addSuffix: true })}</span>
                      <span className="mx-2">•</span>
                      <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                        <MessageSquare size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                        <span>{t('admin.forum.repliesCount', { count: thread.post_count })}</span>
                      </div>
                      <span className="mx-2">•</span>
                      <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                        <Eye size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                        <span>{t('admin.forum.viewsCount', { count: thread.views })}</span>
                      </div>
                    </div>
                  </div>

                  <div className={`flex items-center space-x-2 ml-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      thread.moderation_status === 'approved'
                        ? 'bg-green-900/30 text-green-400'
                        : thread.moderation_status === 'rejected'
                          ? 'bg-red-900/30 text-red-400'
                          : 'bg-yellow-900/30 text-yellow-400'}
                    }`}>
                      {thread.moderation_status === 'approved'
                        ? t('admin.approved')
                        : thread.moderation_status === 'rejected'
                          ? t('admin.rejected')
                          : t('admin.pending')}
                    </span>
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <div
                  className="prose prose-invert max-w-none line-clamp-2 text-sm"
                  dangerouslySetInnerHTML={{ __html: thread.content }}
                />
              </div>

              {thread.moderation_status === 'rejected' && thread.moderation_comment && (
                <div className="mt-4 p-3 bg-red-900/20 border border-red-800/30 rounded-lg">
                  <div className="text-sm text-red-300">
                    <span className="font-medium">{t('admin.forum.rejectionReason')}:</span> {thread.moderation_comment}
                  </div>
                </div>
              )}

              <div className={`mt-6 flex flex-wrap gap-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                <a
                  href={`/forum/thread/${thread.id}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`px-3 py-1.5 bg-indigo-800/50 hover:bg-indigo-700/50 rounded-lg text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  <Eye size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  {t('admin.forum.viewThread')}
                </a>

                {thread.moderation_status === 'pending' && (
                  <>
                    <button
                      onClick={() => handleApprove(thread.id)}
                      disabled={moderatingThread === thread.id}
                      className={`px-3 py-1.5 bg-green-900/30 hover:bg-green-800/50 text-green-400 rounded-lg text-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed ${isRTL ? "flex-row-reverse" : ""}`}
                    >
                      {moderatingThread === thread.id ? (
                        <div className={`animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-green-400 mr-1 ${isRTL ? "space-x-reverse" : ""}`}></div>
                      ) : (
                        <CheckCircle size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                      )}
                      {t('admin.approve')}
                    </button>

                    <button
                      onClick={() => handleReject(thread.id)}
                      disabled={moderatingThread === thread.id}
                      className={`px-3 py-1.5 bg-red-900/30 hover:bg-red-800/50 text-red-400 rounded-lg text-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed ${isRTL ? "flex-row-reverse" : ""}`}
                    >
                      {moderatingThread === thread.id ? (
                        <div className={`animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-red-400 mr-1 ${isRTL ? "space-x-reverse" : ""}`}></div>
                      ) : (
                        <X size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                      )}
                      {t('admin.reject')}
                    </button>

                    <input
                      type="text"
                      placeholder={t('admin.forum.rejectionReasonPlaceholder')}
                      value={moderationComment}
                      onChange={(e) => setModerationComment(e.target.value)}
                      className={`flex-1 min-w-[200px] bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-1.5 px-3 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 ${isRTL ? "flex-row-reverse" : ""}`}
                    />
                  </>
                )}

                <button
                  onClick={() => handleTogglePin(thread.id, thread.is_pinned)}
                  className={`px-3 py-1.5 rounded-lg text-sm flex items-center ${
                    thread.is_pinned
                      ? 'bg-yellow-900/30 hover:bg-yellow-800/50 text-yellow-400'
                      : 'bg-indigo-800/50 hover:bg-indigo-700/50'}
                  }`}
                >
                  <Pin size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  {thread.is_pinned ? t('admin.forum.unpin') : t('admin.forum.pin')}
                </button>

                <button
                  onClick={() => handleToggleLock(thread.id, thread.is_locked)}
                  className={`px-3 py-1.5 rounded-lg text-sm flex items-center ${
                    thread.is_locked
                      ? 'bg-red-900/30 hover:bg-red-800/50 text-red-400'
                      : 'bg-indigo-800/50 hover:bg-indigo-700/50'}
                  }`}
                >
                  {thread.is_locked ? (
                    <>
                      <Unlock size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                      {t('admin.forum.unlock')}
                    </>
                  ) : (
                    <>
                      <Lock size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                      {t('admin.forum.lock')}
                    </>
                  )}
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </DashboardLayout>
  );
};

export default ForumModeration;