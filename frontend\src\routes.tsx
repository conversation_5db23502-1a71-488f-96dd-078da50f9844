import React, { Suspense } from 'react';
import { Routes, Route, Navigate, useLocation, useNavigate } from 'react-router-dom';
import { LoadingFallback } from './components/ui';
import RoleRoute from './components/routing/RoleRoute';
import { RouteConfig } from './routes/routeConfig';
import publicRoutes from './routes/publicRoutes';
import userRoutes from './routes/userRoutes';
import moderatorRoutes from './routes/moderatorRoutes';
import mentorRoutes from './routes/mentorRoutes';
import investorRoutes from './routes/investorRoutes';
import adminRoutes from './routes/adminRoutes';
import superAdminRoutes from './routes/superAdminRoutes';
import PublicLayout from './components/layout/PublicLayout';
import AuthenticatedLayout from './components/layout/AuthenticatedLayout';
import { useAppSelector } from './store/hooks';
import { shouldRedirect, debugRouteMatching } from './utils/routeValidation';
// Removed routeDebugger utilities - using simplified route validation
import {
  getAuthRedirectPath,
  safeRedirect,
  exposeRedirectDebugger,
  isProtectedRoute,
  isAuthRoute
} from './utils/authRedirectManager';
import NotFoundPage from './pages/NotFoundPage';
// ✅ REMOVED: UnifiedAdminRouter - now using MainLayout with UniversalSidebar

/**
 * Helper function to wrap components with Suspense
 */
const withSuspense = (
  Component: React.ComponentType<any>,
  loadingMessage = 'Loading...'
) => (
  <Suspense fallback={<LoadingFallback message={loadingMessage} />}>
    <Component />
  </Suspense>
);

/**
 * Enhanced Smart fallback component with redirect loop prevention
 * Uses centralized redirect manager to prevent infinite redirects
 */
const SmartFallback: React.FC = () => {
  const { user, isAuthenticated, isLoading } = useAppSelector(state => state.auth);
  const location = useLocation();
  const navigate = useNavigate();
  const currentPath = location.pathname;

  // Show loading while authentication state is being determined
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-gray-600 dark:text-gray-300 font-medium">Loading...</span>
        </div>
      </div>
    );
  }

  // Debug route matching in development
  if (process.env.NODE_ENV === 'development') {
    debugRouteMatching(currentPath, user);
    logRouteDebug(currentPath, user, 'SmartFallback');

    // Expose debuggers to window for console access
    exposeRouteDebugger(user);
    exposeRedirectDebugger();
  }

  // Check for authentication-based redirects first
  const authRedirectPath = getAuthRedirectPath(user, currentPath, isAuthenticated);
  if (authRedirectPath) {
    const redirectSuccess = safeRedirect(authRedirectPath, navigate);
    if (redirectSuccess) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`🔄 SmartFallback auth redirect: ${currentPath} → ${authRedirectPath}`);
      }
      return null; // Let the navigation happen
    }
  }

  // Use the existing route validation logic for other cases
  const redirectCheck = shouldRedirect(currentPath, user);

  if (redirectCheck.shouldRedirect && redirectCheck.redirectTo) {
    const redirectSuccess = safeRedirect(redirectCheck.redirectTo, navigate);
    if (redirectSuccess) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`🔄 SmartFallback route redirect: ${currentPath} → ${redirectCheck.redirectTo} (${redirectCheck.reason})`);
      }
      return null; // Let the navigation happen
    } else {
      // Redirect was prevented, show 404 instead
      if (process.env.NODE_ENV === 'development') {
        console.warn(`🚫 SmartFallback redirect prevented, showing 404 for: ${currentPath}`);
      }
    }
  }

  // If we reach here, it's a genuine 404 - show proper 404 page
  return <NotFoundPage />;
};

/**
 * Renders a single route with appropriate protection and layout
 */
const renderRoute = (route: RouteConfig, index: number): React.ReactElement => {
  const RouteComponent = route.component;

  // Create the route element with simple protection
  const protectedElement = route.requireAuth ? (
    <RoleRoute config={route}>
      {withSuspense(RouteComponent, route.loadingMessage)}
    </RoleRoute>
  ) : (
    withSuspense(RouteComponent, route.loadingMessage)
  );

  // Wrap with appropriate layout
  const layoutElement = route.layout === 'public' ? (
    <PublicLayout>{protectedElement}</PublicLayout>
  ) : route.layout === 'authenticated' ? (
    <AuthenticatedLayout>{protectedElement}</AuthenticatedLayout>
  ) : (
    // No layout wrapper for auth pages (login, register, etc.)
    protectedElement
  );

  return (
    <Route
      key={`${route.path}-${index}`}
      path={route.path}
      element={layoutElement}
    />
  );
};

/**
 * SIMPLIFIED: Main application routes - only essential routes
 */
const AppRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Public routes */}
      {publicRoutes.map((route: RouteConfig, index: number) => renderRoute(route, index))}

      {/* User routes */}
      {userRoutes.map((route: RouteConfig, index: number) => renderRoute(route, index + 1000))}

      {/* Role-specific routes */}
      {moderatorRoutes.map((route: RouteConfig, index: number) => renderRoute(route, index + 2000))}
      {mentorRoutes.map((route: RouteConfig, index: number) => renderRoute(route, index + 2500))}
      {investorRoutes.map((route: RouteConfig, index: number) => renderRoute(route, index + 3000))}

      {/* Admin routes */}
      {adminRoutes.map((route: RouteConfig, index: number) => renderRoute(route, index + 4000))}

      {/* Super Admin routes */}
      {superAdminRoutes.map((route: RouteConfig, index: number) => renderRoute(route, index + 5000))}

      {/* Smart fallback route - redirects based on authentication status */}
      <Route path="*" element={<SmartFallback />} />
    </Routes>
  );
};

export default AppRoutes;


